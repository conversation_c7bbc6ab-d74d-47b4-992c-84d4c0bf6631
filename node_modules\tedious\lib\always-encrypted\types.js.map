{"version": 3, "file": "types.js", "names": ["SQLServerEncryptionType", "exports", "DescribeParameterEncryptionResultSet1", "DescribeParameterEncryptionResultSet2", "SQLServerStatementColumnEncryptionSetting"], "sources": ["../../src/always-encrypted/types.ts"], "sourcesContent": ["// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.\n// Copyright (c) 2019 Microsoft Corporation\n\nimport { CEKEntry } from './cek-entry';\nimport { type BaseMetadata } from '../metadata-parser';\n\nexport interface EncryptionKeyInfo {\n  encryptedKey: Buffer;\n  dbId: number;\n  keyId: number;\n  keyVersion: number;\n  mdVersion: Buffer;\n  keyPath: string;\n  keyStoreName: string;\n  algorithmName: string;\n}\n\nexport enum SQLServerEncryptionType {\n  Deterministic = 1,\n  Randomized = 2,\n  PlainText = 0,\n}\n\nexport interface EncryptionAlgorithm {\n  encryptData: (plainText: Buffer) => Buffer;\n  decryptData: (cipherText: Buffer) => Buffer;\n}\n\nexport interface CryptoMetadata {\n  cekEntry?: CEKEntry;\n  cipherAlgorithmId: number;\n  cipherAlgorithmName?: string;\n  normalizationRuleVersion: Buffer;\n  encryptionKeyInfo?: EncryptionKeyInfo;\n  ordinal: number;\n  encryptionType: SQLServerEncryptionType;\n  cipherAlgorithm?: EncryptionAlgorithm;\n  baseTypeInfo?: BaseMetadata;\n}\n\nexport interface HashMap<T> {\n  [hash: string]: T;\n}\n\n\n// Fields in the first resultset of \"sp_describe_parameter_encryption\"\n// We expect the server to return the fields in the resultset in the same order as mentioned below.\n// If the server changes the below order, then transparent parameter encryption will break.\nexport enum DescribeParameterEncryptionResultSet1 {\n  KeyOrdinal,\n  DbId,\n  KeyId,\n  KeyVersion,\n  KeyMdVersion,\n  EncryptedKey,\n  ProviderName,\n  KeyPath,\n  KeyEncryptionAlgorithm\n}\n\n\n// Fields in the second resultset of \"sp_describe_parameter_encryption\"\n// We expect the server to return the fields in the resultset in the same order as mentioned below.\n// If the server changes the below order, then transparent parameter encryption will break.\nexport enum DescribeParameterEncryptionResultSet2 {\n  ParameterOrdinal,\n  ParameterName,\n  ColumnEncryptionAlgorithm,\n  ColumnEncrytionType,\n  ColumnEncryptionKeyOrdinal,\n  NormalizationRuleVersion\n}\n\nexport enum SQLServerStatementColumnEncryptionSetting {\n  /**\n   * if \"Column Encryption Setting=Enabled\" in the connection string, use Enabled. Otherwise, maps to Disabled.\n   */\n  UseConnectionSetting,\n  /**\n   * Enables TCE for the command. Overrides the connection level setting for this command.\n   */\n  Enabled,\n  /**\n   * Parameters will not be encrypted, only the ResultSet will be decrypted. This is an optimization for queries that\n   * do not pass any encrypted input parameters. Overrides the connection level setting for this command.\n   */\n  ResultSetOnly,\n  /**\n   * Disables TCE for the command.Overrides the connection level setting for this command.\n   */\n  Disabled,\n}\n"], "mappings": ";;;;;;AAAA;AACA;AAAA,IAgBYA,uBAAuB,GAAAC,OAAA,CAAAD,uBAAA,0BAAvBA,uBAAuB;EAAvBA,uBAAuB,CAAvBA,uBAAuB;EAAvBA,uBAAuB,CAAvBA,uBAAuB;EAAvBA,uBAAuB,CAAvBA,uBAAuB;EAAA,OAAvBA,uBAAuB;AAAA;AA4BnC;AACA;AACA;AAAA,IACYE,qCAAqC,GAAAD,OAAA,CAAAC,qCAAA,0BAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAAA,OAArCA,qCAAqC;AAAA,OAajD;AACA;AACA;AAAA,IACYC,qCAAqC,GAAAF,OAAA,CAAAE,qCAAA,0BAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAArCA,qCAAqC,CAArCA,qCAAqC;EAAA,OAArCA,qCAAqC;AAAA;AAAA,IASrCC,yCAAyC,GAAAH,OAAA,CAAAG,yCAAA,0BAAzCA,yCAAyC;EAAzCA,yCAAyC,CAAzCA,yCAAyC;EAAzCA,yCAAyC,CAAzCA,yCAAyC;EAAzCA,yCAAyC,CAAzCA,yCAAyC;EAAzCA,yCAAyC,CAAzCA,yCAAyC;EAAA,OAAzCA,yCAAyC;AAAA"}