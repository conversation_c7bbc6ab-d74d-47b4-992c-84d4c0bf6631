/**
 * tables.js
 * Lógica de tablas y navegación por pestañas para el módulo de logística
 * Extraído del bloque JavaScript monolítico en mod_logistica.php
 */

// Función para inicializar el sistema de pestañas
function initTabSystem() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tableSections = document.querySelectorAll('.table-section');

    // Asegurar que la pestaña recepción esté activa por defecto
    tabButtons.forEach(btn => btn.classList.remove('active'));
    const recepcionTab = document.querySelector('[data-tab="recepcion"]');
    if (recepcionTab) {
        recepcionTab.classList.add('active');
    }

    // Asegurar que solo la tabla recepción esté visible
    tableSections.forEach(section => {
        section.style.setProperty('display', 'none', 'important');
    });
    const recepcionSection = document.querySelector('[data-table="recepcion"]');
    if (recepcionSection) {
        recepcionSection.style.setProperty('display', 'block', 'important');
    }

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');

            // Remover clase active de todos los botones
            tabButtons.forEach(btn => btn.classList.remove('active'));
            // Agregar clase active al botón clickeado
            this.classList.add('active');

            // PRIMERO: Ocultar TODAS las secciones de tabla con !important
            tableSections.forEach(section => {
                section.style.setProperty('display', 'none', 'important');
            });

            // SEGUNDO: Mostrar SOLO la sección correspondiente con !important
            const targetSection = document.querySelector(`[data-table="${tabName}"]`);
            if (targetSection) {
                targetSection.style.setProperty('display', 'block', 'important');
            }
            
            // Si es la tabla de reversa, invalidar el cache para forzar recarga
            // ya que podría haber recibido actualizaciones mientras estaba en segundo plano
            if (tabName === 'reversa') {
                invalidateTableCache('reversa');
            }

            // TERCERO: Cargar datos de la tabla si no están cargados
            loadTableData(tabName);
        });
    });
}

// Función para renderizar datos en la tabla
function renderTableData(tableName, datos) {
    const tableBody = document.getElementById(`${tableName}TableBody`);
    if (!tableBody) {
        console.error(`❌ No se encontró el tbody para la tabla ${tableName}`);
        return;
    }

    if (!datos || datos.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="3" class="text-center text-muted">
                    <i class="bi bi-inbox me-2"></i>
                    No hay datos disponibles para ${tableName}
                </td>
            </tr>
        `;
        return;
    }

    let html = '';
    datos.forEach(fila => {
        // Determinar el estado y la clase CSS correspondiente
        let estado = 'Pendiente';
        let clase_estado = 'pendiente';

        // Lógica específica por tabla
        switch (tableName) {
            case 'directa':
                estado = fila.Semantica || 'Pendiente';
                break;
            case 'faltante':
                estado = fila.Semantica || 'Faltante';
                clase_estado = 'faltante';
                break;
            case 'recepcion':
                estado = fila.tipo_movimiento || 'Pendiente por recibir';
                clase_estado = 'recibido';
                break;
            case 'reversa':
                estado = fila.Semantica || 'Reversa';
                clase_estado = 'reversa';
                break;
        }

        // Asignar clase CSS basada en el estado
        const estadoLower = estado.toLowerCase();
        
        // Caso especial para Rechaza bodega - naranja oscuro
        if (estadoLower === 'rechaza bodega' || estadoLower === 'rechaza bodega justificación') {
            clase_estado = 'rechaza-bodega';
        }
        // Verificar si es un estado de rechazo (contiene la palabra 'rechaza')
        else if (estadoLower.includes('rechaza')) {
            clase_estado = 'rechazo';
        }
        // Verificar casos específicos de rechazo por ID conocidos
        else if (
            estadoLower === 'faltante' ||
            estadoLower.includes('rechazo') ||
            estadoLower === 'rechaza tecnico' ||
            estadoLower === 'rechaza supervisor' ||
            estadoLower === 'rechaza supervisor reversa'
        ) {
            clase_estado = 'rechazo';
        }
        // Estado disponible - verde con texto blanco
        else if (estado.toLowerCase() === 'disponible') {
            clase_estado = 'disponible';
        }
        // Todos los demás estados - amarillo con texto negro
        else {
            clase_estado = 'default';
        }

        // Generar la fila HTML - Botones específicos para cada tabla
        const serialNumber = fila.Serial || fila.serie || 'N/A';
        
        if (tableName === 'recepcion') {
            // Calcular variableCondicion como en el original
            const idMovimiento = fila.id_movimiento || '';
            const idTecnicoDest = fila.id_tecnico_destino || fila.id_transfer || '';
            const variableCondicion = (idMovimiento == 15) ? "Si" : idTecnicoDest;
            
            html += `
                <tr data-type="${tableName}" data-serial="${serialNumber}">
                    <td>${serialNumber}</td>
                    <td><span class="status-badge ${clase_estado}">${estado}</span></td>
                    <td>
                        <div class="action-buttons-container">
                            <button class="action-btn historial-btn" title="Ver Historial" data-serie="${serialNumber}">
                                <i class="bi bi-clock-history"></i>
                            </button>
                            <button class="action-btn aceptar-btn btn btn-success" title="Aceptar Material" 
                                    onclick="actualizarRegistro('${serialNumber}', '${variableCondicion}', '${idMovimiento}', 'ACEPTA')">
                                <i class="bi bi-check-circle"></i>
                            </button>
                            <button class="action-btn justificar-btn" title="Rechazar Material" 
                                    onclick="rechazoMaterial('${serialNumber}', '${variableCondicion}', '${idTecnicoDest}', 'RECHAZA')">
                                <i class="bi bi-journal-x"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        } else {
            // Definir botones para todas las tablas
            let actionButtonsHtml = ``;
            
            // Configuración especial para la tabla directa
            if (tableName === 'directa') {
                actionButtonsHtml = `
                    <button class="action-btn historial-btn" title="Ver Historial" data-serie="${serialNumber}">
                        <i class="bi bi-clock-history"></i>
                    </button>
                    <button class="action-btn declarar-btn" title="Declarar" data-serie="${serialNumber}" data-id-movimiento="${fila.id_movimiento || '1'}">
                        <i class="bi bi-clipboard-check"></i>
                    </button>
                    <button class="action-btn justificar-btn transfiere-btn" title="Transferir" data-serie="${serialNumber}" 
                            onclick="redirigirEnTransferencia('${serialNumber}', '${fila.Item || serialNumber}', '${fila.id_movimiento || '1'}', 'TRANSFIERE')">
                        <i class="bi bi-send-fill"></i>
                    </button>
                `;
            } else if (tableName === 'reversa') {
                // Botones específicos para la tabla de reversa
                actionButtonsHtml = `
                    <button class="action-btn historial-btn" title="Ver Historial" data-serie="${serialNumber}">
                        <i class="bi bi-clock-history"></i>
                    </button>
                    <button class="action-btn declarar-rev-btn" title="Declarar Entrega" data-serie="${serialNumber}" data-id-movimiento="${fila.id_movimiento || '0'}">
                        <i class="bi bi-box-seam"></i>
                    </button>
                    <button class="action-btn transferir-rev-btn" title="A Supervisor" data-serie="${serialNumber}" data-id-movimiento="${fila.id_movimiento || '0'}">
                        <i class="bi bi-send"></i>
                    </button>
                `;
            } else {
                // Botón estándar para otras tablas
                actionButtonsHtml = `
                    <button class="action-btn historial-btn" title="Ver Historial" data-serie="${serialNumber}">
                        <i class="bi bi-clock-history"></i>
                    </button>
                    <button class="action-btn declarar-btn" title="Declarar" data-serie="${serialNumber}" data-id-movimiento="${fila.id_movimiento || '1'}">
                        <i class="bi bi-clipboard-check"></i>
                    </button>
                    <button class="action-btn justificar-btn" title="Justificar" data-serie="${serialNumber}">
                        <i class="bi bi-shield-check"></i>
                    </button>
                `;
            }
            
            html += `
                <tr data-type="${tableName}" data-serial="${serialNumber}">
                    <td>${serialNumber}</td>
                    <td><span class="status-badge ${clase_estado}">${estado}</span></td>
                    <td>
                        <div class="action-buttons-container">
                            ${actionButtonsHtml}
                        </div>
                    </td>
                </tr>
            `;
        }
    });

    tableBody.innerHTML = html;

    // Reinicializar event listeners para los botones de acción
    setupActionButtons();
}

// Función para configurar botones de acción
function setupActionButtons() {
    console.log('🔄 Configurando botones de acción en tablas');
    
    // Botones de historial
    const historialButtons = document.querySelectorAll('.historial-btn');
    console.log(`📊 Encontrados ${historialButtons.length} botones de historial`);
    
    historialButtons.forEach((button) => {
        // Remover listeners previos
        button.removeEventListener('click', handleHistorialClick);
        // Agregar nuevo listener
        button.addEventListener('click', handleHistorialClick);
    });

    // Botones de declarar
    const declararButtons = document.querySelectorAll('.declarar-btn');
    declararButtons.forEach(button => {
        button.removeEventListener('click', handleDeclararClick);
        button.addEventListener('click', handleDeclararClick);
    });

    // Botones de justificar (excluyendo los botones de rechazo)
    const justificarButtons = document.querySelectorAll('.justificar-btn:not([onclick*="rechazoMaterial"])');
    justificarButtons.forEach(button => {
        button.removeEventListener('click', handleJustificarClick);
        button.addEventListener('click', handleJustificarClick);
    });
    
    // Botones de transferir (que no tengan onclick)
    const transfiereButtons = document.querySelectorAll('.transfiere-btn:not([onclick])');
    transfiereButtons.forEach(button => {
        button.removeEventListener('click', handleTransfiereClick);
        button.addEventListener('click', handleTransfiereClick);
    });
    
    // Botones de aceptar (clase correcta aceptar-btn)
    const acceptButtons = document.querySelectorAll('.aceptar-btn');
    console.log(`✅ Encontrados ${acceptButtons.length} botones de aceptar`);
    acceptButtons.forEach(button => {
        // Preservar onclick original antes de removerlo
        const originalOnclick = button.getAttribute('onclick');
        if (originalOnclick) {
            button.setAttribute('data-original-onclick', originalOnclick);
            button.removeAttribute('onclick');
        }
        button.removeEventListener('click', handleAcceptClick);
        button.addEventListener('click', handleAcceptClick);
    });
    
    // Botones de rechazar (clase correcta justificar-btn para recepción)
    const rejectButtons = document.querySelectorAll('.justificar-btn[onclick*="rechazoMaterial"]');
    console.log(`❌ Encontrados ${rejectButtons.length} botones de rechazar`);
    rejectButtons.forEach(button => {
        // Preservar onclick original antes de removerlo
        const originalOnclick = button.getAttribute('onclick');
        if (originalOnclick) {
            button.setAttribute('data-original-onclick', originalOnclick);
            // REMOVER COMPLETAMENTE el onclick para evitar ejecución duplicada
            button.removeAttribute('onclick');
            button.onclick = null; // Asegurar eliminación completa
        }
        button.removeEventListener('click', handleRejectClick);
        // Usar capture: true para que nuestro handler se ejecute primero
        button.addEventListener('click', handleRejectClick, { capture: true });
    });
}

// Handler para botón historial
function handleHistorialClick(e) {
    e.preventDefault();
    
    const serie = e.currentTarget.getAttribute('data-serie');

    if (!serie) {
        console.error('❌ No se pudo obtener la serie del botón');
        return;
    }

    // Abrir offcanvas de historial
    const offcanvasElement = document.getElementById('offcanvasHistorial');
    const serieInput = document.getElementById('serieHistorial');
    
    if (offcanvasElement && serieInput) {
        // Establecer valor de serie en el campo
        serieInput.value = serie;
        
        // Utilizar la API de Bootstrap para abrir el offcanvas
        const historialOffcanvas = new bootstrap.Offcanvas(offcanvasElement);
        historialOffcanvas.show();
        
        // Cargar historial después de mostrar el offcanvas
        setTimeout(() => {
            // Llamar a la función de carga de historial
            cargarHistorialDirecto(serie);
        }, 300);
    } else {
        console.error('❌ No se encontraron elementos del offcanvas de historial');
    }
}

// Handler para botón declarar
function handleDeclararClick(e) {
    e.preventDefault();
    
    const serie = e.currentTarget.getAttribute('data-serie');
    const idMovimiento = e.currentTarget.getAttribute('data-id-movimiento');
    
    if (!serie) {
        console.error('❌ No se pudo obtener la serie del botón');
        return;
    }
    
    // Llamar función de declarar instalación
    if (typeof declararInstalacion === 'function') {
        declararInstalacion(serie, idMovimiento);
    } else {
        console.error('❌ Función declararInstalacion no encontrada');
    }
}

// Handler para botón justificar
function handleJustificarClick(e) {
    e.preventDefault();
    
    const serie = e.currentTarget.getAttribute('data-serie');
    
    if (!serie) {
        console.error('❌ No se pudo obtener la serie del botón');
        return;
    }
    
    // Mostrar offcanvas de justificación (necesita verificar el ID correcto)
    const offcanvasElement = document.getElementById('offcanvasrigh');
    if (offcanvasElement) {
        const justificarOffcanvas = new bootstrap.Offcanvas(offcanvasElement);
        justificarOffcanvas.show();
    }
}

// Handler para botón transferir
function handleTransfiereClick(e) {
    e.preventDefault();
    
    const serie = e.currentTarget.getAttribute('data-serie');
    
    if (!serie) {
        console.error('❌ No se pudo obtener la serie del botón');
        return;
    }
    
    // Mostrar offcanvas de transferencia
    const offcanvasElement = document.getElementById('offcanvasrigh');
    if (offcanvasElement) {
        const transferirOffcanvas = new bootstrap.Offcanvas(offcanvasElement);
        transferirOffcanvas.show();
    }
}

// Función para inicializar sistema de búsqueda
function initSearchSystem() {
    const searchInput = document.getElementById('searchInput');
    const clearButton = document.getElementById('clearSearch');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const visibleTable = document.querySelector('.table-section[style*="block"]');

            if (visibleTable) {
                const rows = visibleTable.querySelectorAll('tbody tr:not(.loading-row):not(.error-row)');
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
        });
    }

    if (clearButton) {
        clearButton.addEventListener('click', function() {
            if (searchInput) {
                searchInput.value = '';
            }
            // Mostrar todas las filas (excepto loading y error)
            const allRows = document.querySelectorAll('.table-section tbody tr:not(.loading-row):not(.error-row)');
            allRows.forEach(row => {
                row.style.display = '';
            });
        });
    }
}

// Función para limpiar cache de tabla específica
function clearTableCache(tableName) {
    const tableCache = window.ModLogisticaConfig.tableCache;
    const loadingState = window.ModLogisticaConfig.loadingState;
    
    if (tableCache[tableName]) {
        console.log(`🗑️ Limpiando cache para tabla ${tableName}`);
        tableCache[tableName] = null;
        loadingState[tableName] = false;
        
        const tabButton = document.querySelector(`[data-tab="${tableName}"]`);
        if (tabButton) {
            tabButton.classList.remove('loaded');
        }
        
        // Recargar datos
        loadTableData(tableName);
    }
}

// Handler para botón aceptar
function handleAcceptClick(e) {
    e.preventDefault();
    
    // Obtener parámetros del onclick original
    const onclickValue = e.currentTarget.getAttribute('data-original-onclick') || '';
    
    // Extraer parámetros usando regex desde la fila de la tabla
    const row = e.currentTarget.closest('tr');
    const serialCell = row ? row.querySelector('td:first-child') : null;
    const serie = serialCell ? serialCell.textContent.trim() : '';
    
    // Buscar los parámetros en el onclick original si existe
    let variableCondicion = '';
    let idMovimiento = '';
    
    if (onclickValue.includes('actualizarRegistro')) {
        const matches = onclickValue.match(/actualizarRegistro\('([^']+)',\s*'([^']+)',\s*'([^']+)',\s*'([^']+)'\)/);
        if (matches) {
            variableCondicion = matches[2];
            idMovimiento = matches[3];
        }
    }
    
    console.log('✅ Aceptando material:', { serie, variableCondicion, idMovimiento });
    
    if (!serie) {
        console.error('❌ Serial no encontrado para aceptar material');
        return;
    }
    
    if (typeof window.actualizarRegistro === 'function') {
        window.actualizarRegistro(serie, variableCondicion, idMovimiento, 'ACEPTA');
    } else {
        console.error('❌ Función actualizarRegistro no disponible');
    }
}

// Handler para botón rechazar
function handleRejectClick(e) {
    e.preventDefault();
    e.stopImmediatePropagation(); // Evitar que otros handlers se ejecuten
    
    // Obtener parámetros del onclick original
    const onclickValue = e.currentTarget.getAttribute('data-original-onclick') || '';
    
    // Extraer parámetros usando regex desde la fila de la tabla
    const row = e.currentTarget.closest('tr');
    const serialCell = row ? row.querySelector('td:first-child') : null;
    const serie = serialCell ? serialCell.textContent.trim() : '';
    
    // Buscar los parámetros en el onclick original
    let ticket = '';
    let idDestino = '';
    
    if (onclickValue.includes('rechazoMaterial')) {
        // Regex mejorada para manejar strings vacíos y espacios
        const matches = onclickValue.match(/rechazoMaterial\(\s*'([^']*)',\s*'([^']*)',\s*'([^']*)',\s*'([^']*)'\s*\)/);
        if (matches) {
            // matches[1] = serie, matches[2] = ticket, matches[3] = idDestino, matches[4] = accion
            ticket = matches[2];
            idDestino = matches[3];
            console.log('🔍 Parámetros extraídos:', { 
                serie_onclick: matches[1], 
                ticket_onclick: matches[2], 
                idDestino_onclick: matches[3], 
                accion_onclick: matches[4] 
            });
        } else {
            console.warn('⚠️ No se pudieron extraer parámetros del onclick:', onclickValue);
        }
    }
    
    console.log('❌ Rechazando material:', { serie, ticket, idDestino, onclick: onclickValue });
    
    if (!serie) {
        console.error('❌ Serial no encontrado para rechazar material');
        return;
    }
    
    // Si ticket está vacío, usar el ID del usuario actual (como hace el PHP)
    const ticketFinal = ticket || (window.userId || window.ModLogisticaConfig?.user?.id || '');
    console.log('📋 Llamando rechazoMaterial con:', { serie, ticket: ticketFinal, idDestino });
    
    // Verificar si la función está disponible en ModLogisticaHandlers
    if (window.ModLogisticaHandlers && typeof window.ModLogisticaHandlers.rechazoMaterial === 'function') {
        console.log('✅ [TABLES] Función rechazoMaterial encontrada en ModLogisticaHandlers, ejecutando...');
        window.ModLogisticaHandlers.rechazoMaterial(serie, ticketFinal, idDestino, 'RECHAZA');
    } else if (typeof window.rechazoMaterial === 'function') {
        console.log('✅ [TABLES] Función rechazoMaterial encontrada en window, ejecutando...');
        window.rechazoMaterial(serie, ticketFinal, idDestino, 'RECHAZA');
    } else {
        console.error('❌ [TABLES] Función rechazoMaterial no disponible');
        console.log('🔍 [TABLES] window.rechazoMaterial type:', typeof window.rechazoMaterial);
        console.log('🔍 [TABLES] ModLogisticaHandlers disponible:', !!window.ModLogisticaHandlers);

        // Fallback: intentar con timeout para esperar a que se cargue
        setTimeout(() => {
            if (window.ModLogisticaHandlers && typeof window.ModLogisticaHandlers.rechazoMaterial === 'function') {
                console.log('✅ [TABLES] Función rechazoMaterial encontrada en ModLogisticaHandlers después de timeout, ejecutando...');
                window.ModLogisticaHandlers.rechazoMaterial(serie, ticketFinal, idDestino, 'RECHAZA');
            } else if (typeof window.rechazoMaterial === 'function') {
                console.log('✅ [TABLES] Función rechazoMaterial encontrada en window después de timeout, ejecutando...');
                window.rechazoMaterial(serie, ticketFinal, idDestino, 'RECHAZA');
            } else {
                console.error('❌ [TABLES] Función rechazoMaterial sigue no disponible después de timeout');
            }
        }, 100);
    }
}

// Exportar funciones al scope global para compatibilidad
window.ModLogisticaTables = {
    initTabSystem,
    renderTableData,
    setupActionButtons,
    handleHistorialClick,
    handleDeclararClick,
    handleJustificarClick,
    handleTransfiereClick,
    handleAcceptClick,
    handleRejectClick,
    initSearchSystem,
    clearTableCache
};

// Compatibilidad con funciones globales existentes
window.initTabSystem = initTabSystem;
window.renderTableData = renderTableData;
window.setupActionButtons = setupActionButtons;
window.initSearchSystem = initSearchSystem;
window.clearTableCache = clearTableCache;

// Log de inicialización
if (window.ModLogisticaConfig && window.ModLogisticaConfig.debug) {
    console.log('📋 Tables.js cargado - Lógica de tablas y navegación inicializada');
}