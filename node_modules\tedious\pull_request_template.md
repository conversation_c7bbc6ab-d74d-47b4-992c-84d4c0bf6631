**Before submitting a PR :**
1. Ensure your fork is created from `master` branch of [the repository](https://github.com/tediousjs/tedious).
2. Run `npm install` in the root folder.
3. After bug fix/code change, ensure all the existing tests and new tests (if any) pass (`npm run-script test-all`). During development, to run individual test use `node_modules/nodeunit test/<test_file.js> -t <test_name>`.
4. Build the driver (`npm run build`).
5. Run eslint and flow typechecker (`npm run lint`).
6. Run commitlint (`node_modules/.bin/commitlint --from origin/master --to HEAD`). Refer [commit conventions](https://commitlint.js.org/#/concepts-commit-conventions) and [commit rules](https://commitlint.js.org/#/reference-rules).

**Thank you for Contributing!**
