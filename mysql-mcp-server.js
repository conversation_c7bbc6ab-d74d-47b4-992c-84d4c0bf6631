#!/usr/bin/env node

const mysql = require('mysql2/promise');

// Configuración de la base de datos
const DB_CONFIG = {
  host: '**************',
  port: 3306,
  user: 'ncornejo',
  password: 'N1c0l7as17',
  database: 'operaciones_tqw',
  charset: 'utf8mb4',
  timezone: '+00:00'
};

class MySQLMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'mysql-database',
        version: '0.1.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.connection = null;
    this.setupToolHandlers();
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection(DB_CONFIG);
      console.error('✅ Conectado exitosamente a MySQL');
      return true;
    } catch (error) {
      console.error('❌ Error conectando a MySQL:', error.message);
      return false;
    }
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'read_query',
            description: 'Execute SELECT queries to read data from the database',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'SQL SELECT query to execute'
                }
              },
              required: ['query']
            }
          },
          {
            name: 'write_query',
            description: 'Execute INSERT, UPDATE, or DELETE queries',
            inputSchema: {
              type: 'object',
              properties: {
                query: {
                  type: 'string',
                  description: 'SQL INSERT, UPDATE, or DELETE query to execute'
                }
              },
              required: ['query']
            }
          },
          {
            name: 'list_tables',
            description: 'Get a list of all tables in the database',
            inputSchema: {
              type: 'object',
              properties: {}
            }
          },
          {
            name: 'describe_table',
            description: 'View schema information for a specific table',
            inputSchema: {
              type: 'object',
              properties: {
                table_name: {
                  type: 'string',
                  description: 'Name of the table to describe'
                }
              },
              required: ['table_name']
            }
          }
        ]
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'read_query':
            return await this.executeReadQuery(args.query);
          
          case 'write_query':
            return await this.executeWriteQuery(args.query);
          
          case 'list_tables':
            return await this.listTables();
          
          case 'describe_table':
            return await this.describeTable(args.table_name);
          
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`
            }
          ]
        };
      }
    });
  }

  async executeReadQuery(query) {
    if (!this.connection) {
      throw new Error('No database connection');
    }

    // Validar que sea una query SELECT
    const trimmedQuery = query.trim().toLowerCase();
    if (!trimmedQuery.startsWith('select') && !trimmedQuery.startsWith('show') && !trimmedQuery.startsWith('describe')) {
      throw new Error('Only SELECT, SHOW, and DESCRIBE queries are allowed with read_query');
    }

    try {
      const [rows] = await this.connection.execute(query);
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(rows, null, 2)
          }
        ]
      };
    } catch (error) {
      throw new Error(`SQL Error: ${error.message}`);
    }
  }

  async executeWriteQuery(query) {
    if (!this.connection) {
      throw new Error('No database connection');
    }

    try {
      const [result] = await this.connection.execute(query);
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              affectedRows: result.affectedRows,
              insertId: result.insertId,
              message: 'Query executed successfully'
            }, null, 2)
          }
        ]
      };
    } catch (error) {
      throw new Error(`SQL Error: ${error.message}`);
    }
  }

  async listTables() {
    if (!this.connection) {
      throw new Error('No database connection');
    }

    try {
      const [rows] = await this.connection.execute('SHOW TABLES');
      const tables = rows.map(row => Object.values(row)[0]);
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(tables, null, 2)
          }
        ]
      };
    } catch (error) {
      throw new Error(`SQL Error: ${error.message}`);
    }
  }

  async describeTable(tableName) {
    if (!this.connection) {
      throw new Error('No database connection');
    }

    try {
      const [rows] = await this.connection.execute(`DESCRIBE \`${tableName}\``);
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(rows, null, 2)
          }
        ]
      };
    } catch (error) {
      throw new Error(`SQL Error: ${error.message}`);
    }
  }

  async run() {
    const connected = await this.connect();
    if (!connected) {
      process.exit(1);
    }

    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('🚀 MySQL MCP Server iniciado');
  }
}

// Iniciar el servidor
const server = new MySQLMCPServer();
server.run().catch(console.error);
