<?php
/**
 * test_sse_mod_logistica.php
 * Archivo de prueba para verificar el funcionamiento del nuevo SSE independiente
 */

// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir conexión a la base de datos
include("con_db.php");

// ID de usuario de prueba (cambiar por un ID válido)
$test_user_id = 73; // Cambiar por un ID de usuario válido

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test SSE Mod Logística</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-badge.disponible { background-color: #d4edda; color: #155724; }
        .status-badge.pendiente { background-color: #fff3cd; color: #856404; }
        .status-badge.transito { background-color: #cce5ff; color: #004085; }
        .status-badge.faltante { background-color: #f8d7da; color: #721c24; }
        .status-badge.reversa { background-color: #e2e3e5; color: #383d41; }
        .status-badge.instalada { background-color: #d1ecf1; color: #0c5460; }
        .status-badge.rechazado { background-color: #f5c6cb; color: #721c24; }
        
        .action-buttons-container {
            display: flex;
            gap: 5px;
        }
        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
        }
        .historial-btn { background-color: #ffc107; color: #000; }
        .accept-btn { background-color: #28a745; color: #fff; }
        .reject-btn { background-color: #dc3545; color: #fff; }
        .declarar-btn { background-color: #17a2b8; color: #fff; }
        .transfiere-btn { background-color: #6c757d; color: #fff; }
        .justificar-btn { background-color: #fd7e14; color: #fff; }
        .declarar-rev-btn { background-color: #20c997; color: #fff; }
        .transferir-rev-btn { background-color: #6f42c1; color: #fff; }
        
        .sse-status {
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: bold;
            z-index: 1000;
        }
        .sse-connected { background-color: #d4edda; color: #155724; }
        .sse-error { background-color: #fff3cd; color: #856404; }
        .sse-failed { background-color: #f8d7da; color: #721c24; }
        
        .table-container {
            margin-bottom: 30px;
        }
        .loading-row {
            text-align: center;
            font-style: italic;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <h1>Test SSE Mod Logística</h1>
        <p>Usuario de prueba: <strong><?php echo $test_user_id; ?></strong></p>
        
        <!-- Indicador de estado SSE -->
        <div id="sse-status" class="sse-status">⚪ Iniciando...</div>
        
        <!-- Controles -->
        <div class="row mb-3">
            <div class="col-12">
                <button id="startSSE" class="btn btn-success">Iniciar SSE</button>
                <button id="stopSSE" class="btn btn-danger">Detener SSE</button>
                <button id="clearLogs" class="btn btn-warning">Limpiar Logs</button>
            </div>
        </div>
        
        <!-- Log de mensajes SSE -->
        <div class="row mb-4">
            <div class="col-12">
                <h3>Log de Mensajes SSE</h3>
                <div id="sseLog" class="border p-3" style="height: 200px; overflow-y: auto; background-color: #f8f9fa;">
                    <em>Los mensajes SSE aparecerán aquí...</em>
                </div>
            </div>
        </div>
        
        <!-- Tablas de prueba -->
        <div class="row">
            <!-- Tabla Directa -->
            <div class="col-md-6 table-container">
                <h3>Tabla Directa</h3>
                <table class="table table-striped" id="directaTable">
                    <thead>
                        <tr>
                            <th>Serie</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody id="directaTableBody">
                        <tr class="loading-row">
                            <td colspan="3">Esperando datos SSE...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Tabla Faltante -->
            <div class="col-md-6 table-container">
                <h3>Tabla Faltante</h3>
                <table class="table table-striped" id="faltanteTable">
                    <thead>
                        <tr>
                            <th>Serie</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody id="faltanteTableBody">
                        <tr class="loading-row">
                            <td colspan="3">Esperando datos SSE...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="row">
            <!-- Tabla Recepción -->
            <div class="col-md-6 table-container">
                <h3>Tabla Recepción</h3>
                <table class="table table-striped" id="recepcionTable">
                    <thead>
                        <tr>
                            <th>Serie</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody id="recepcionTableBody">
                        <tr class="loading-row">
                            <td colspan="3">Esperando datos SSE...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Tabla Reversa -->
            <div class="col-md-6 table-container">
                <h3>Tabla Reversa</h3>
                <table class="table table-striped" id="reversaTable">
                    <thead>
                        <tr>
                            <th>Serie</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody id="reversaTableBody">
                        <tr class="loading-row">
                            <td colspan="3">Esperando datos SSE...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Configuración global para el test
        window.ModLogisticaConfig = {
            user: {
                id: <?php echo $test_user_id; ?>
            }
        };
        
        // Variables globales para SSE
        let testEventSource = null;
        
        // Función para agregar mensaje al log
        function addLogMessage(message, type = 'info') {
            const logDiv = document.getElementById('sseLog');
            const timestamp = new Date().toLocaleTimeString();
            const messageDiv = document.createElement('div');
            messageDiv.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            
            switch(type) {
                case 'error':
                    messageDiv.style.color = '#dc3545';
                    break;
                case 'success':
                    messageDiv.style.color = '#28a745';
                    break;
                case 'warning':
                    messageDiv.style.color = '#ffc107';
                    break;
                default:
                    messageDiv.style.color = '#6c757d';
            }
            
            logDiv.appendChild(messageDiv);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // Función para iniciar SSE
        function startTestSSE() {
            if (testEventSource) {
                testEventSource.close();
            }
            
            const userId = <?php echo $test_user_id; ?>;
            const url = `sse_mod_logistica.php?user_id=${userId}&timestamp=${Date.now()}`;
            
            addLogMessage(`🚀 Iniciando conexión SSE: ${url}`, 'info');
            
            testEventSource = new EventSource(url);
            
            testEventSource.onopen = function() {
                addLogMessage('✅ Conexión SSE establecida', 'success');
                updateSSEStatus('connected');
            };
            
            testEventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    addLogMessage(`📨 Mensaje recibido: ${JSON.stringify(data)}`, 'info');
                    
                    // Procesar mensaje usando la misma lógica que mod_logistica
                    if (typeof processLogisticaSSEMessage === 'function') {
                        processLogisticaSSEMessage(data);
                    }
                } catch (error) {
                    addLogMessage(`❌ Error procesando mensaje: ${error.message}`, 'error');
                }
            };
            
            testEventSource.onerror = function(error) {
                addLogMessage(`❌ Error en conexión SSE: ${error}`, 'error');
                updateSSEStatus('error');
            };
        }
        
        // Función para detener SSE
        function stopTestSSE() {
            if (testEventSource) {
                testEventSource.close();
                testEventSource = null;
                addLogMessage('🔌 Conexión SSE cerrada', 'warning');
                updateSSEStatus('disconnected');
            }
        }
        
        // Función para actualizar estado SSE
        function updateSSEStatus(status) {
            const statusElement = document.getElementById('sse-status');
            statusElement.className = 'sse-status sse-' + status;
            
            switch (status) {
                case 'connected':
                    statusElement.textContent = '🟢 Conectado';
                    break;
                case 'error':
                    statusElement.textContent = '🟡 Error';
                    break;
                case 'disconnected':
                    statusElement.textContent = '🔴 Desconectado';
                    break;
                default:
                    statusElement.textContent = '⚪ Iniciando...';
            }
        }
        
        // Event listeners para botones
        document.getElementById('startSSE').addEventListener('click', startTestSSE);
        document.getElementById('stopSSE').addEventListener('click', stopTestSSE);
        document.getElementById('clearLogs').addEventListener('click', function() {
            document.getElementById('sseLog').innerHTML = '<em>Log limpiado...</em>';
        });
        
        // Funciones dummy para evitar errores
        window.redirigirEnTransferencia = function(serie, item, idMovimiento, accion) {
            addLogMessage(`🔄 redirigirEnTransferencia: ${serie}, ${accion}`, 'info');
        };
        
        window.actualizarRegistro = function(serie, ticket, idMovimiento, accion) {
            addLogMessage(`✅ actualizarRegistro: ${serie}, ${accion}`, 'success');
        };
        
        window.rechazoMaterial = function(serie, ticket, idDestino, accion) {
            addLogMessage(`❌ rechazoMaterial: ${serie}, ${accion}`, 'warning');
        };
        
        // Auto-iniciar SSE al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            addLogMessage('📄 Página cargada, iniciando SSE automáticamente...', 'info');
            setTimeout(startTestSSE, 1000);
        });
    </script>
    
    <!-- Cargar el sse-handler.js para usar las mismas funciones -->
    <script src="js/mod_logistica/sse-handler.js"></script>
</body>
</html>
