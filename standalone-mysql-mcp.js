#!/usr/bin/env node

// Servidor MCP MySQL standalone - sin dependencias externas del SDK
const net = require('net');
const { spawn } = require('child_process');

// Configuración de la base de datos
const DB_CONFIG = {
  host: '**************',
  port: 3306,
  user: 'ncornejo',
  password: 'N1c0l7as17',
  database: 'operaciones_tqw'
};

// Función para ejecutar consultas MySQL usando el cliente mysql2
async function executeQuery(query) {
  return new Promise((resolve, reject) => {
    const mysql = require('mysql2');
    const connection = mysql.createConnection(DB_CONFIG);
    
    connection.connect((err) => {
      if (err) {
        reject(new Error(`Connection failed: ${err.message}`));
        return;
      }
    });

    connection.query(query, (error, results) => {
      connection.end();
      
      if (error) {
        reject(new Error(`Query failed: ${error.message}`));
      } else {
        resolve(results);
      }
    });
  });
}

// Manejador de requests MCP
async function handleRequest(request) {
  const response = {
    jsonrpc: "2.0",
    id: request.id
  };

  try {
    switch (request.method) {
      case 'initialize':
        response.result = {
          protocolVersion: "2024-11-05",
          capabilities: { tools: {} },
          serverInfo: { name: "mysql-database", version: "0.1.0" }
        };
        break;

      case 'tools/list':
        response.result = {
          tools: [
            {
              name: 'read_query',
              description: 'Execute SELECT queries to read data from the database',
              inputSchema: {
                type: 'object',
                properties: { query: { type: 'string' } },
                required: ['query']
              }
            },
            {
              name: 'list_tables',
              description: 'Get a list of all tables in the database',
              inputSchema: { type: 'object', properties: {} }
            },
            {
              name: 'describe_table',
              description: 'View schema information for a specific table',
              inputSchema: {
                type: 'object',
                properties: { table_name: { type: 'string' } },
                required: ['table_name']
              }
            }
          ]
        };
        break;

      case 'tools/call':
        const { name, arguments: args } = request.params;
        
        switch (name) {
          case 'read_query':
            const trimmedQuery = args.query.trim().toLowerCase();
            if (!trimmedQuery.startsWith('select') && !trimmedQuery.startsWith('show') && !trimmedQuery.startsWith('describe')) {
              throw new Error('Only SELECT, SHOW, and DESCRIBE queries are allowed');
            }
            const results = await executeQuery(args.query);
            response.result = { 
              content: [{ type: 'text', text: JSON.stringify(results, null, 2) }] 
            };
            break;

          case 'list_tables':
            const tables = await executeQuery('SHOW TABLES');
            const tableNames = tables.map(row => Object.values(row)[0]);
            response.result = { 
              content: [{ type: 'text', text: JSON.stringify(tableNames, null, 2) }] 
            };
            break;

          case 'describe_table':
            const schema = await executeQuery(`DESCRIBE \`${args.table_name}\``);
            response.result = { 
              content: [{ type: 'text', text: JSON.stringify(schema, null, 2) }] 
            };
            break;

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
        break;

      default:
        response.result = {};
    }
  } catch (error) {
    response.error = {
      code: -32000,
      message: error.message
    };
  }

  return response;
}

// Función principal
async function main() {
  console.error('🚀 Standalone MySQL MCP Server iniciado');
  
  // Test de conexión inicial
  try {
    await executeQuery('SELECT 1 as test');
    console.error('✅ Conexión a MySQL exitosa');
  } catch (error) {
    console.error('❌ Error de conexión:', error.message);
    process.exit(1);
  }

  // Procesar entrada de stdin
  process.stdin.setEncoding('utf8');
  let buffer = '';

  process.stdin.on('data', async (chunk) => {
    buffer += chunk;
    const lines = buffer.split('\n');
    buffer = lines.pop();

    for (const line of lines) {
      if (line.trim()) {
        try {
          const request = JSON.parse(line);
          const response = await handleRequest(request);
          console.log(JSON.stringify(response));
        } catch (error) {
          console.error('Error procesando request:', error.message);
        }
      }
    }
  });

  process.stdin.on('end', () => {
    process.exit(0);
  });
}

main().catch((error) => {
  console.error('Error fatal:', error.message);
  process.exit(1);
});
