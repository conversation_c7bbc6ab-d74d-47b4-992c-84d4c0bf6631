{"name": "dist-tqw", "version": "1.0.0", "main": "calidad_reactiva_antigua.js", "packageManager": "npm@latest", "directories": {"test": "test"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "node dev-server.js", "dev-php": "node php-dev-server.js", "serve": "npx serve . -p 3005"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@stagewise/toolbar": "^0.4.8", "chokidar": "^4.0.3", "cors": "^2.8.5", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "ws": "^8.18.2"}, "dependencies": {"@executeautomation/database-server": "^1.1.0", "@modelcontextprotocol/sdk": "^1.13.2", "@types/node": "^24.0.7", "mysql2": "^3.14.1"}}