#!/usr/bin/env node

const mysql = require('mysql2/promise');

// Configuración de la base de datos
const DB_CONFIG = {
  host: '**************',
  port: 3306,
  user: 'ncornejo',
  password: 'N1c0l7as17',
  database: 'operaciones_tqw',
  charset: 'utf8mb4',
  timezone: '+00:00'
};

let connection = null;

async function connectToDatabase() {
  try {
    connection = await mysql.createConnection(DB_CONFIG);
    console.error('✅ Conectado exitosamente a MySQL');
    return true;
  } catch (error) {
    console.error('❌ Error conectando a MySQL:', error.message);
    return false;
  }
}

async function handleRequest(request) {
  const response = {
    jsonrpc: "2.0",
    id: request.id
  };

  try {
    switch (request.method) {
      case 'tools/list':
        response.result = {
          tools: [
            {
              name: 'read_query',
              description: 'Execute SELECT queries to read data from the database',
              inputSchema: {
                type: 'object',
                properties: {
                  query: { type: 'string', description: 'SQL SELECT query to execute' }
                },
                required: ['query']
              }
            },
            {
              name: 'list_tables',
              description: 'Get a list of all tables in the database',
              inputSchema: { type: 'object', properties: {} }
            },
            {
              name: 'describe_table',
              description: 'View schema information for a specific table',
              inputSchema: {
                type: 'object',
                properties: {
                  table_name: { type: 'string', description: 'Name of the table to describe' }
                },
                required: ['table_name']
              }
            }
          ]
        };
        break;

      case 'tools/call':
        const { name, arguments: args } = request.params;
        
        switch (name) {
          case 'read_query':
            const trimmedQuery = args.query.trim().toLowerCase();
            if (!trimmedQuery.startsWith('select') && !trimmedQuery.startsWith('show') && !trimmedQuery.startsWith('describe')) {
              throw new Error('Only SELECT, SHOW, and DESCRIBE queries are allowed');
            }
            const [rows] = await connection.execute(args.query);
            response.result = { content: [{ type: 'text', text: JSON.stringify(rows, null, 2) }] };
            break;

          case 'list_tables':
            const [tables] = await connection.execute('SHOW TABLES');
            const tableNames = tables.map(row => Object.values(row)[0]);
            response.result = { content: [{ type: 'text', text: JSON.stringify(tableNames, null, 2) }] };
            break;

          case 'describe_table':
            const [schema] = await connection.execute(`DESCRIBE \`${args.table_name}\``);
            response.result = { content: [{ type: 'text', text: JSON.stringify(schema, null, 2) }] };
            break;

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
        break;

      case 'initialize':
        response.result = {
          protocolVersion: "2024-11-05",
          capabilities: {
            tools: {}
          },
          serverInfo: {
            name: "mysql-database",
            version: "0.1.0"
          }
        };
        break;

      default:
        throw new Error(`Unknown method: ${request.method}`);
    }
  } catch (error) {
    response.error = {
      code: -32000,
      message: error.message
    };
  }

  return response;
}

async function main() {
  const connected = await connectToDatabase();
  if (!connected) {
    process.exit(1);
  }

  console.error('🚀 MySQL MCP Server iniciado');

  // Leer entrada de stdin línea por línea
  process.stdin.setEncoding('utf8');
  let buffer = '';

  process.stdin.on('data', async (chunk) => {
    buffer += chunk;
    const lines = buffer.split('\n');
    buffer = lines.pop(); // Mantener la línea incompleta en el buffer

    for (const line of lines) {
      if (line.trim()) {
        try {
          const request = JSON.parse(line);
          const response = await handleRequest(request);
          console.log(JSON.stringify(response));
        } catch (error) {
          console.error('Error procesando request:', error.message);
        }
      }
    }
  });

  process.stdin.on('end', async () => {
    if (connection) {
      await connection.end();
    }
    process.exit(0);
  });
}

main().catch(console.error);
