/**
 * ui.js
 * Funciones de manipulación de interfaz de usuario para el módulo de logística
 * Extraído del bloque JavaScript monolítico en mod_logistica.php
 */

// Función para mostrar indicador de carga en la tabla
function showTableLoading(tableName) {
    const tableBody = document.getElementById(`${tableName}TableBody`);
    if (tableBody) {
        tableBody.innerHTML = `
            <tr class="loading-row">
                <td colspan="3" class="text-center">
                    <div class="loading-container" style="color: #00e1fd; padding: 20px;">
                        <div class="spinner-border spinner-border-sm me-2" role="status" style="color: #00e1fd; border-color: #00e1fd transparent #00e1fd transparent;">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                        <span style="color: #00e1fd; font-weight: 500;">Cargando datos de ${tableName}...</span>
                    </div>
                </td>
            </tr>
        `;
    }
}

// Función para mostrar error en la tabla
function showTableError(tableName, errorMessage) {
    const tableBody = document.getElementById(`${tableName}TableBody`);
    if (tableBody) {
        tableBody.innerHTML = `
            <tr class="error-row">
                <td colspan="3" class="text-center text-danger">
                    <div class="error-container">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Error al cargar datos: ${errorMessage}
                        <br>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadTableData('${tableName}')">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            Reintentar
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }
}

// Función para mostrar notificaciones toast
function mostrarNotificacion(mensaje, tipo = 'info') {
    const tiposValidos = ['success', 'error', 'warning', 'info'];
    if (!tiposValidos.includes(tipo)) {
        tipo = 'info';
    }

    const colores = {
        success: '#28a745',
        error: '#dc3545', 
        warning: '#ffc107',
        info: '#17a2b8'
    };

    // Crear elemento de notificación
    const notificacion = document.createElement('div');
    notificacion.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colores[tipo]};
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        z-index: 10000;
        max-width: 300px;
        opacity: 0;
        transition: opacity 0.3s ease;
        font-weight: 500;
    `;
    notificacion.textContent = mensaje;

    document.body.appendChild(notificacion);

    // Mostrar con animación
    setTimeout(() => {
        notificacion.style.opacity = '1';
    }, 100);

    // Ocultar y eliminar después de 3 segundos
    setTimeout(() => {
        notificacion.style.opacity = '0';
        setTimeout(() => {
            if (notificacion.parentNode) {
                notificacion.parentNode.removeChild(notificacion);
            }
        }, 300);
    }, 3000);
}

// Función para mostrar modal de rechazo
function mostrarModalRechazo(Serial, ticket, id_tecnico_destino, accion) {
    const modal = document.getElementById('popup');
    if (modal) {
        modal.style.display = 'flex';
        
        // Configurar campos ocultos si existen
        const serialField = document.getElementById('serial_rechazar');
        const ticketField = document.getElementById('ticket_rechazar');
        const tecnicoField = document.getElementById('id_tecnico_destino_rechazar');
        const accionField = document.getElementById('accion_rechazar');
        
        if (serialField) serialField.value = Serial;
        if (ticketField) ticketField.value = ticket;
        if (tecnicoField) tecnicoField.value = id_tecnico_destino;
        if (accionField) accionField.value = accion;
    }
}

// Función para ocultar modal de rechazo
function ocultarModalRechazo() {
    const modal = document.getElementById('popup');
    if (modal) {
        modal.style.display = 'none';
        
        // Limpiar campos
        const motivoField = document.getElementById('motivoRechazo');
        if (motivoField) motivoField.value = '';
    }
}

// Función para mostrar/ocultar elementos de transferencia
function toggleElementTransferencia(elementId, show = true) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = show ? 'block' : 'none';
    }
}

// Función para manejar visibilidad de secciones de transferencia
function mostrarSeccionTransferencia(seccion) {
    // Ocultar todas las secciones
    const secciones = [
        'tecnicoTransf',
        'motivo_tran_contain', 
        'motivoASuper',
        'serie_tran_contain',
        'divArchivo'
    ];
    
    secciones.forEach(id => toggleElementTransferencia(id, false));
    
    // Mostrar la sección requerida
    toggleElementTransferencia(seccion, true);
}

// Función para añadir spinner a botón
function addButtonSpinner(buttonElement, texto = 'Procesando...') {
    if (buttonElement) {
        buttonElement.innerHTML = `
            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            ${texto}
        `;
        buttonElement.disabled = true;
    }
}

// Función para remover spinner de botón
function removeButtonSpinner(buttonElement, textoOriginal) {
    if (buttonElement) {
        buttonElement.innerHTML = textoOriginal;
        buttonElement.disabled = false;
    }
}

// Función para animar la eliminación de filas
function removeRowWithAnimation(row, callback) {
    if (row) {
        row.classList.add('row-fadeout');
        
        setTimeout(() => {
            if (row.parentNode) {
                row.parentNode.removeChild(row);
            }
            if (typeof callback === 'function') {
                callback();
            }
        }, 300);
    }
}

// Función para actualizar estado de tab buttons
function updateTabButtonState(tabName, state) {
    const tabButton = document.querySelector(`[data-tab="${tabName}"]`);
    if (tabButton) {
        // Remover todos los estados
        tabButton.classList.remove('loading', 'loaded', 'active');
        
        // Añadir el estado requerido
        if (state) {
            tabButton.classList.add(state);
        }
    }
}

// Función para limpiar modal-open del body
function removeModalOpenClass() {
    document.body.classList.remove('modal-open');
}

// Función para filtrar filas de tabla
function filterTableRows(tableBodyId, searchTerm) {
    const tableBody = document.getElementById(tableBodyId);
    if (tableBody) {
        const rows = tableBody.querySelectorAll('tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const matches = text.includes(searchTerm.toLowerCase());
            row.style.display = matches ? '' : 'none';
        });
    }
}

// Función para crear badge de estado
function createStatusBadge(estado, texto = null) {
    const badgeText = texto || estado;
    const badgeClass = getStatusBadgeClass(estado);
    
    return `<span class="badge ${badgeClass}">${badgeText}</span>`;
}

// Función auxiliar para obtener clase de badge según estado
function getStatusBadgeClass(estado) {
    const estadoLower = estado.toLowerCase();
    
    if (estadoLower.includes('pendiente') || estadoLower.includes('espera')) {
        return 'bg-warning text-dark';
    } else if (estadoLower.includes('completado') || estadoLower.includes('instalado')) {
        return 'bg-success';
    } else if (estadoLower.includes('rechazado') || estadoLower.includes('error')) {
        return 'bg-danger';
    } else if (estadoLower.includes('transferido') || estadoLower.includes('enviado')) {
        return 'bg-info';
    } else {
        return 'bg-secondary';
    }
}

// Función para centrar modal en pantalla
function centerModal(modalElement) {
    if (modalElement) {
        modalElement.style.cssText += `
            display: flex;
            align-items: center;
            justify-content: center;
        `;
    }
}

// Función para resetear formulario
function resetForm(formId) {
    const form = document.getElementById(formId);
    if (form) {
        form.reset();
        
        // Limpiar cualquier mensaje de error
        const errorElements = form.querySelectorAll('.text-danger, .invalid-feedback');
        errorElements.forEach(element => {
            element.textContent = '';
            element.style.display = 'none';
        });
        
        // Remover clases de validación
        const inputElements = form.querySelectorAll('.form-control');
        inputElements.forEach(input => {
            input.classList.remove('is-invalid', 'is-valid');
        });
    }
}

// Exportar funciones al scope global para compatibilidad
window.ModLogisticaUI = {
    showTableLoading,
    showTableError,
    mostrarNotificacion,
    mostrarModalRechazo,
    ocultarModalRechazo,
    toggleElementTransferencia,
    mostrarSeccionTransferencia,
    addButtonSpinner,
    removeButtonSpinner,
    removeRowWithAnimation,
    updateTabButtonState,
    removeModalOpenClass,
    filterTableRows,
    createStatusBadge,
    centerModal,
    resetForm
};

// Compatibilidad con funciones globales existentes
window.mostrarNotificacion = mostrarNotificacion;
window.showTableLoading = showTableLoading;
window.showTableError = showTableError;
// NOTA: rechazoMaterial y Rechazocancelar se exportan desde handlers.js
// window.rechazoMaterial = mostrarModalRechazo; // COMENTADO - Causa conflictos
// window.Rechazocancelar = ocultarModalRechazo; // COMENTADO - Causa conflictos

// Log de inicialización
if (window.ModLogisticaConfig && window.ModLogisticaConfig.debug) {
    console.log('🎨 UI.js cargado - Funciones de interfaz de usuario inicializadas');
}