/**
 * config.js
 * Configuración global para el módulo de logística
 * Extraído del bloque JavaScript monolítico en mod_logistica.php
 */

// Inicializar configuración global si no existe (será sobrescrita por PHP)
if (typeof window.ModLogisticaConfig === 'undefined') {
    window.ModLogisticaConfig = {
        endpoints: {
            main: 'GET_LOGIS_DIRECTA.php',
            historial: 'GET_LOGISTICA.php',
            tecnicos: 'GET_LOGISTICA.php?accion=lista_tecnicos',
            precio: 'GET_LOGISTICA.php',
            actualizarTabla: 'GET_LOGISTICA.php',
            loadTable: 'api_mod_logis_load_table.php'
        },
        user: {
            id: null,
            nombre: null,
            rut: null,
            nombreShort: null,
            area: null,
            perfil: null
        },
        validation: {
            patterns: {
                rut: /^[0-9]{7,8}-[0-9kK]{1}$/,
                ordenTrabajo: /^[0-9A-Za-z\-]+$/,
                serie: /^[A-Za-z0-9\-]+$/
            },
            text: {
                motivoMinLength: 10,
                observacionesMinLength: 10,
                observacionesMaxLength: 500
            },
            archivo: {
                maxSize: 5 * 1024 * 1024, // 5MB
                allowedTypes: [
                    'image/jpeg',
                    'image/jpg', 
                    'image/png',
                    'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                ]
            }
        },
        ui: {
            spinnerClass: 'spinner-border spinner-border-sm',
            notificationDuration: 3000,
            loadingDelay: 1000
        },
        debug: true
    };
}

// Cache para almacenar datos ya cargados
window.ModLogisticaConfig.tableCache = {
    directa: null,
    faltante: null,
    recepcion: null,
    reversa: null
};

// Estado de carga de las tablas
window.ModLogisticaConfig.loadingState = {
    directa: false,
    faltante: false,
    recepcion: false,
    reversa: false
};

// Variable global userId (será inicializada desde PHP)
if (typeof window.userId === 'undefined') {
    // Intentar obtener userId de variables globales
    window.userId = (typeof userId !== 'undefined') ? userId : 
                   (window.ModLogisticaConfig?.user?.id || null);
}

// Funciones auxiliares de configuración
window.ModLogistica = {
    getConfig: function(path) {
        const keys = path.split('.');
        let value = window.ModLogisticaConfig;
        
        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return null;
            }
        }
        
        return value;
    },
    
    debugLog: function(message, data = null) {
        if (window.ModLogisticaConfig.debug) {
            console.log('[ModLogistica]', message, data || '');
        }
    },
    
    errorLog: function(message, error = null) {
        console.error('[ModLogistica Error]', message, error || '');
    }
};

// Log de inicialización
if (window.ModLogisticaConfig.debug) {
    console.log('🔧 Config.js cargado - Configuración del módulo de logística inicializada');
}
