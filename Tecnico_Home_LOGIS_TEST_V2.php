<?php
/**
 * Tecnico_Home_LOGIS_TEST_V2.php
 * Versión mejorada que trabaja exclusivamente con variables de sesión
 */

// Encabezados para permitir la navegación mientras se mantiene la sesión
header('Content-Type: text/html; charset=UTF-8');

// Permitir cierto nivel de caché para facilitar la navegación hacia atrás
// Pero usando validación para asegurar contenido fresco
header('Cache-Control: private, must-revalidate');
header('Pragma: private');

// Garantizar que no estamos destruyendo cookies de sesión accidentalmente
ini_set('session.use_cookies', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_lifetime', 86400); // 24 horas de duración para la cookie
ini_set('session.gc_maxlifetime', 86400); // 24 horas de vida útil para los datos de sesión

// Configuración de errores (para desarrollo)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Habilitar cookies para las sesiones
ini_set('session.use_cookies', 1);

// Incluir conexión a la base de datos
$inc = include("con_db.php");

// Iniciar sesión si no está iniciada
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Función para mostrar mensaje de sesión no iniciada
function mostrarMensajeSesion() {
    // Almacenar la URL actual en localStorage para referencia
    echo '<!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Sesión Requerida</title>
        <link rel="stylesheet" href="css/bootstrap.min.css">
        <!-- Estilos movidos a tecnico_home_logis.css -->
    </head>
    <body>
        <div class="session-card">
            <h3>Sesión no encontrada</h3>
            <p class="message">La sesión no se encuentra activa o ha expirado.</p>
            <div class="btn-group">
                <button id="btnVolver" class="btn btn-secondary">Volver Atrás</button>
                <a href="login.php" class="btn btn-primary">Iniciar Sesión</a>
            </div>

            <div class="debug-info">
                <p>Si estás viendo esta pantalla frecuentemente, es posible que tu sesión no se esté guardando correctamente.</p>
                <p>Intenta las siguientes acciones:</p>
                <ul>
                    <li>Eliminar cookies y caché del navegador</li>
                    <li>Abrir el enlace en una nueva pestaña</li>
                    <li>Usar el enlace "Iniciar Sesión" para iniciar una nueva sesión</li>
                </ul>
            </div>
        </div>

        <script>
        // Registrar la página actual y anterior en localStorage
        if (typeof localStorage !== "undefined") {
            var currentPage = window.location.href;
            var previousPages = JSON.parse(localStorage.getItem("navigationHistory") || "[]");

            // Añadir la página actual al historial si no está ya
            if (previousPages.indexOf(currentPage) === -1) {
                previousPages.push(currentPage);
                // Mantener solo las últimas 5 páginas
                if (previousPages.length > 5) {
                    previousPages = previousPages.slice(-5);
                }
                localStorage.setItem("navigationHistory", JSON.stringify(previousPages));
            }
        }

        // Mejorar el funcionamiento del botón volver atrás
        document.getElementById("btnVolver").addEventListener("click", function(e) {
            e.preventDefault();

            // Primero intentar con el historial del navegador
            if (window.history.length > 1) {
                window.history.go(-1);
                return;
            }

            // Si no hay historial de navegador, intentar con localStorage
            if (typeof localStorage !== "undefined") {
                var previousPages = JSON.parse(localStorage.getItem("navigationHistory") || "[]");

                if (previousPages.length > 1) {
                    // Ir a la penúltima página (la anterior a la actual)
                    var prevPage = previousPages[previousPages.length - 2];
                    window.location.href = prevPage;
                    return;
                }
            }

            // Si todo falla, ir a una página segura
            window.location.href = "activity_dashboard.php";
        });
        </script>
    </body>
    </html>';
    exit();
}

// Verificar si existen las variables de sesión necesarias
// Necesitamos al menos una de las variables de sesión
if (
    ((!isset($_SESSION['rut']) || empty($_SESSION['rut'])) &&
    (!isset($_SESSION['id_sesion']) || empty($_SESSION['id_sesion'])))
) {
    // No hay variables de sesión, mostrar mensaje
    if (function_exists('logToFile')) {
        logToFile("Sesión no iniciada en Tecnico_Home_LOGIS_TEST_V2", "AUTH", [
            'rut' => isset($_SESSION['rut']) ? 'definido' : 'no definido',
            'id_sesion' => isset($_SESSION['id_sesion']) ? 'definido' : 'no definido'
        ]);
    }

    // Verificar si venimos de una navegación hacia atrás
    $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
    if (strpos($referer, $_SERVER['HTTP_HOST']) !== false) {
        // Si venimos del mismo dominio, intentar recuperar la sesión
        session_regenerate_id(false);
        // Si aún tenemos al menos una variable de sesión, no redirigir
        if (isset($_SESSION['rut']) || isset($_SESSION['id_sesion'])) {
            // Intentar recuperar datos faltantes
            if (!isset($_SESSION['rut']) && isset($_SESSION['id_sesion'])) {
                // Intentar recuperar rut desde id_sesion
                // Código para recuperar rut si es necesario
            } elseif (isset($_SESSION['rut']) && !isset($_SESSION['id_sesion'])) {
                // Intentar recuperar id_sesion desde rut
                // Código para recuperar id_sesion si es necesario
            }
        } else {
            mostrarMensajeSesion();
        }
    } else {
        mostrarMensajeSesion();
    }
}

// Obtener datos de sesión
$rut_usuario = $_SESSION['rut'] ?? null;
$sesion = $_SESSION['id_sesion'] ?? null;

// Preparar la consulta SQL basada en los datos de sesión disponibles
if (!empty($rut_usuario)) {
    // Si tenemos RUT, consultar por RUT (prioridad)
    $sql = "SELECT tut.nombre, tut.email, tut.rut AS RUT, tut.id,
            tut.PERFIL, tut2.id as id_supervisor,
            tut2.nombre AS nombre_supervisor,
            tut.rut AS RUT_OR,
            tut.nombre_short
            FROM tb_user_tqw tut
            LEFT JOIN tb_user_tqw tut2
            ON tut2.email = tut.correo_super
            WHERE tut.rut = '$rut_usuario'";
} elseif (!empty($sesion)) {
    // Si solo tenemos token de sesión, consultar por token
    $sql = "SELECT tut.nombre, tut.email, tla.RUT, tut.id,
            tut.PERFIL, tut2.id as id_supervisor,
            tut2.nombre AS nombre_supervisor,
            tut.rut AS RUT_OR,
            tut.nombre_short
            FROM TB_LOG_APP tla
            LEFT JOIN tb_user_tqw tut
            ON tla.RUT = tut.rut
            LEFT JOIN tb_user_tqw tut2
            ON tut2.email = tut.correo_super
            WHERE TOKEN = '$sesion'";
} else {
    // No deberíamos llegar aquí debido a la verificación anterior, pero por seguridad
    mostrarMensajeSesion();
}

// Ejecutar la consulta
$result = mysqli_query($conex, $sql);

// Verificar si obtuvimos resultados
if ($result && mysqli_num_rows($result) > 0) {
    $row = mysqli_fetch_assoc($result);

    // Actualizar datos de sesión si es necesario
    $_SESSION['rut'] = $row['RUT'];
    $_SESSION['nombre'] = $row['nombre'];
    $_SESSION['id_usuario'] = $row['id'];
    $_SESSION['perfil'] = $row['PERFIL'];

    // Registrar el estado de la sesión en localStorage para diagnóstico
    echo "<script>
    if (typeof localStorage !== 'undefined') {
        localStorage.setItem('ultimaSesionActiva', '" . date('Y-m-d H:i:s') . "');
        localStorage.setItem('ultimoRutActivo', '" . $_SESSION['rut'] . "');
        localStorage.setItem('ultimaPaginaConSesion', window.location.href);
    }
    </script>";

    // Asignar valores a las variables que usa el resto del script
    $nombre = $row['nombre'];
    $email = $row['email'];
    $perfil = $row['PERFIL'];
    $rut_ejecut = $row['RUT_OR'];
    $id_usuario = $row['id'];
    $id_supervisor = $row['id_supervisor'];
    $usuario = $row['RUT']; // Esta variable se usa en consultas posteriores
    $nombre_short = $row['nombre_short'];
    $supervisor = $row['nombre_supervisor'];
} else {
    // No se encontraron datos o hubo un error en la consulta
    if (function_exists('logToFile')) {
        logToFile("Error en consulta SQL en Tecnico_Home_LOGIS_TEST_V2", "ERROR", [
            'sql' => $sql,
            'error' => mysqli_error($conex)
        ]);
    }
    mostrarMensajeSesion();
}

    $nombre = $row['nombre'];
    $email = $row['email'];
    $perfil = $row['PERFIL'];
    $rut_ejecut = $row['RUT_OR'];
    $id_usuario = $row['id'];
    $id_supervisor = $row['id_supervisor'];
    /////////////////////////////////////////////////////
    $usuario = $row['RUT'];
    $nombre_short = $row['nombre_short'];
    $supervisor = $row['nombre_supervisor'];

    $mate = "SELECT desc_material , desc_familia
    FROM TP_materiales
    order by desc_material asc";
    $materiales = $conex->query($mate);






    $asignacion = $conex->query(
        "
       SELECT RZ.Serial
        , RZ.Item
        , RZ.Org
        , RZ.Subinventory
        , RZ.`Locator`
        , RZ.State
        , tlm.tipo_movimiento
        , RZ.Status
        , RZ.`Receipt Date`
            id_tecnico_origen
            , '' id_tecnico_destino
            , '' observacion
            ,`Unit Number`
            ,  MOVIMI.id_movimiento
            , '' motivo
            , '' ticket
            ,tfd.descripcion
            , tfd.familia
            , '' as fechax
            ,  CASE
                WHEN Job is null THEN 0
                WHEN Job = '' THEN 0
                ELSE Job
            END as Job
            , Semantica
            , Flag_trans
                , tpve.monto_valor
                , CASE
                    WHEN MOVIMI.id_movimiento = 15 THEN MOVIMI.id_tecnico_origen
                    ELSE 164
                    END id_transfer
        FROM (SELECT Serial, Item, Org, Revision
                    , CASE
                         WHEN `Locator` = 'ANALISIS' THEN Subinventory
                            WHEN Y.serie is not null THEN tut2.rut
                            else Subinventory
                        END as Subinventory
                    , `Locator`
                        , Operation, Job, Step, Lot, State, Status, `Receipt Date`
                        , `Ship Date`, `Supplier Name`, `Supplier Lot`
                        , `Supplier Serial`, `Unit Number`, `Attributes`, `[  ]`
                        , `Unnamed: 20`, fecha_carga
                        , tut2.rut
                        , CASE
                        WHEN Y.serie is not null THEN 'Si'
                        else 'No'
                    END as Flag_trans
                        FROM TB_FERRET_DIRECTA1
                    AS DIRECTA
        LEFT JOIN
        (SELECT serie, id_destino,rn
            FROM (
            SELECT serie , id_destino , row_number() OVER (PARTITION BY serie ORDER BY fecha DESC) rn
            FROM TB_LOGIS_TECNICO_SERIE_TRANSFIERE
            ) x WHERE rn = 1
        ) Y
        ON DIRECTA.Serial = Y.serie
        LEFT JOIN tb_user_tqw tut2 ON tut2.id  = Y.id_destino
        ) RZ
        LEFT JOIN tb_logis_movimientos MOVIMI ON `MOVIMI`.id = RZ.`Unit Number`
        LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm          ON MOVIMI.id_movimiento = tlm.id
        LEFT JOIN       tb_user_tqw tut      ON         tut.rut = RZ.Subinventory
        LEFT JOIN tp_ferret_desc tfd  ON tfd.ID  = RZ.Item
        LEFT JOIN TP_LOGIST_VALORES_EQUIPO tpve ON tpve.descripcion_modelo_equipo = tfd.descripcion
        WHERE (
            MOVIMI.id_movimiento IS NULL OR
            (MOVIMI.id_movimiento IN (2, 0, 9, 15, 21) AND MOVIMI.flag_bodega_final != 1)
        )
        AND CASE
            WHEN  RZ.State IS NULL THEN 0
            WHEN RZ.State = 'Issued out of stores' THEN 1
            else 0
        END = 0
        AND tut.id  = " . $id_usuario . "
        "
    );



    $stock = $conex->query(" SELECT RZ.Serial
            , RZ.Item
                    ,Subinventory
                    , RZ.`Attributes` as  id_movimiento
                    , tlm.tipo_movimiento
                    ,tfd.descripcion  , tfd.familia
                    , Semantica
                    , job
                    , tpve.monto_valor
                    ,fecha_carga
                    , tp_familia
            FROM
            (
            SELECT Serial, Item, Org, Revision
            , CASE
                    WHEN Y.serie is not null THEN tut2.rut
                    else Subinventory
                END as Subinventory
            , `Locator`
            , Operation
            , CASE
                    WHEN job is null THEN 0
                    WHEN job = '' THEN 0
                    ELSE job
                END as job
            , Step, Lot, State, Status, `Receipt Date`
            , `Ship Date`, `Supplier Name`, `Supplier Lot`
            , `Supplier Serial`, `Unit Number`, `Attributes`, `[  ]`
            , `Unnamed: 20`, fecha_carga
            , tut2.rut
            FROM tb_ferret_directa1
            AS DIRECTA
            LEFT JOIN
            (select * from
                (
                SELECT
                *, ROW_NUMBER() OVER (PARTITION BY serie ORDER BY fecha DESC) AS row_num_trans
                    FROM TB_LOGIS_TECNICO_SERIE_TRANSFIERE
                ) YY
                    WHERE YY.row_num_trans = 1
            )Y
            ON DIRECTA.Serial  = Y.serie
            LEFT JOIN tb_user_tqw tut2 ON tut2.id  = Y.id_destino
            ) RZ
            LEFT JOIN tb_logis_movimientos MOVIMI ON `MOVIMI`.id = RZ.`Unit Number`
            LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm          ON MOVIMI.id_movimiento = tlm.id
            LEFT JOIN             tb_user_tqw tut          ON             tut.rut = RZ.Subinventory
            LEFT JOIN tp_ferret_desc tfd          ON tfd.ID  = RZ.Item
            LEFT JOIN TP_LOGIST_VALORES_EQUIPO tpve ON tpve.descripcion_modelo_equipo = tfd.descripcion
            WHERE `MOVIMI`.id IS NOT NULL
            and CASE
                WHEN MOVIMI.id_movimiento = 2 THEN 'NO'
                WHEN MOVIMI.id_movimiento = 9 THEN 'NO'
                WHEN MOVIMI.id_movimiento = 15 THEN 'NO'
                WHEN MOVIMI.id_movimiento = 0 THEN 'NO'
                WHEN MOVIMI.id_movimiento = 21 THEN 'NO'
                WHEN MOVIMI.id_movimiento = 5 THEN 'NO'
                WHEN MOVIMI.id_movimiento = 12 THEN 'NO'
                ELSE 'SI'
            END = 'SI'
            AND tut.id  = '" . $id_usuario . "'
                AND RZ.State <> 'Issued out of stores'
                ");






    $bd_reversa = $conex->query("
    SELECT RZ.Serial
    , RZ.Item
    , RZ.Org
    , RZ.Subinventory
    , SUBSTRING(`Locator`  , 1, LENGTH(`Locator`) - 2) as `Locator`
    , RZ.State
    , RZ.Status
    , tlm.tipo_movimiento
    , RZ.`Receipt Date`
    ,   fecha_hora, A.serie,
        id_tecnico_origen
        , id_tecnico_destino
        , observacion
        , A.id_movimiento
        , motivo, ticket , tut.Nombre_short
        , tfd.descripcion
        , tfd.familia
        , case
        WHEN A.id_movimiento is null then 'Pendiente por entregar'
        WHEN A.id_movimiento = 0  then 'Pendiente por entregar'
                else  Semantica
        end as Semantica
    FROM
        (
            SELECT Serial, Item, Org, Revision, Subinventory, `Locator`, Operation, Job
        , Step, Lot, State, Status, `Receipt Date`, `Ship Date`, `Supplier Name`
        , `Supplier Lot`, `Supplier Serial`, `Unit Number`, `Attributes`, `[  ]`, `Unnamed: 20`
        FROM TB_LOGIST_bdReversa

        ) RZ
    LEFT JOIN
    (
    SELECT id, fecha_hora, serie,
        id_tecnico_origen, id_tecnico_destino,
        observacion, id_movimiento, motivo , ticket
    FROM (
        SELECT id, fecha_hora, serie,
            id_tecnico_origen, id_tecnico_destino,ticket,
            observacion, id_movimiento, motivo,
            ROW_NUMBER() OVER (PARTITION BY serie ORDER BY fecha_hora DESC) AS row_num
        FROM TB_LOGIS_MOVIMIENTOS
    ) AS ranked
    WHERE row_num = 1
    ) A
    ON RZ.Serial = A.serie
    LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm      ON A.id_movimiento = tlm.id
    LEFT JOIN         tb_user_tqw tut      ON
        SUBSTRING(tut.rut, 1, LENGTH(tut.rut) - 2)  = SUBSTRING(`Locator`, 1, LENGTH(`Locator`) - 2)
    LEFT JOIN         tb_user_tqw tut_destino      ON         tut_destino.id = A.id_tecnico_destino
    LEFT JOIN         tp_ferret_desc tfd      ON RZ.Item  = tfd.ID
    WHERE CASE
        WHEN A.id_movimiento = 12 THEN 'NO'
        ELSE 'SI'
    END = 'SI'
    AND RZ.State <> 'Issued out of stores'
                AND  tut.id  = " . $id_usuario . "
                ");


    $dotacion = $conex->query(
        "
                SELECT '' AS id, '' AS email, '' AS pass, '' AS reg_date, '' AS nombre, '' AS area
                , '' AS supervisor, '' AS rut, '' AS correo_super, '' AS iden_user, '' AS vigente, '' AS ZONA_GEO, '' AS nombre_ndc, '' AS Nombre_short, '' AS PERFIL
                UNION ALL
                SELECT id, email, pass, reg_date, nombre, area
                , supervisor, rut, correo_super, iden_user, vigente, ZONA_GEO, nombre_ndc, Nombre_short, PERFIL
                FROM tb_user_tqw
                WHERE vigente = 'Si'
                AND PERFIL IN ('TECNICO RESIDENCIAL')
                ORDER BY nombre asc
                    "
    );

    $dotrow = mysqli_fetch_assoc($dotacion);

    // @Tecnico_Home_LOGIS.php:2181-2197
    $dotacion2 = $conex->query(
        "
                SELECT '' AS id, '' AS email, '' AS pass, '' AS reg_date, '' AS nombre, '' AS area
                , '' AS supervisor, '' AS rut, '' AS correo_super, '' AS iden_user, '' AS vigente, '' AS ZONA_GEO, '' AS nombre_ndc, '' AS Nombre_short, '' AS PERFIL
                UNION ALL
                SELECT id, email, pass, reg_date, nombre, area
                , supervisor, rut, correo_super, iden_user, vigente, ZONA_GEO, nombre_ndc, Nombre_short, PERFIL
                FROM tb_user_tqw
                WHERE vigente = 'Si'
                AND PERFIL IN ('TECNICO RESIDENCIAL','TECNICO REDES')
                ORDER BY nombre asc
                    "
    );



    // @Tecnico_Home_LOGIS.php:2181-2197
    $dotacion_qa = $conex->query(
        "

                SELECT id, email, pass, reg_date, nombre, area
                , supervisor, rut, correo_super, iden_user, vigente, ZONA_GEO, nombre_ndc, Nombre_short, PERFIL
                FROM tb_user_tqw
                WHERE vigente = 'Si'
                AND PERFIL  = 'user_QA'
                ORDER BY nombre asc
                    "
    );

    // $dotrow2 = mysqli_fetch_assoc($dotacion2);


    $cierre_inventario = $conex->query("
    SELECT '' as id_cierre,
        '' as fecha,
        '' as Serial,
        '' as id_tecnico,
        '' as supervisor,
        '' as SerieCargada,
        '' as ESTADO_APP,
        '' as Semantica,
        '' as flujo,
        '' as id,
        '' as fecha_hora,
        '' as serie,
        '' as id_tecnico_origen,
        '' as id_tecnico_destino,
        '' as observacion,
        '' as id_movimiento_actual,
        '' as motivo,
        '' as ticket,
        '' as monto_valor,
        '' as id_valor_tecnico,
        '' as descuento,
        '' as orden,
        '' as rut,
        '' as archivo
        UNION ALL
    SELECT id_cierre,
                        tlci.fecha,
                        tlci.Serial,
                        id_tecnico,
                        supervisor,
                        SerieCargada,
                        ESTADO_APP,
                        CASE
                            WHEN tlm.Semantica IS NULL  THEN
                                CASE
                                WHEN flujo = 'Reversa' THEN 'Pendiente por entregar'
                                WHEN flujo = 'Directa' THEN 'Entregado'
                            END
                            WHEN disponi.`Serial` is null and flujo = 'Directa'
                            and A.id_movimiento <> 20
                                THEN 'Fuera Organización'
                            ELSE tlm.Semantica
                        END
                        AS Semantica,
                        flujo,
                        monto_valor id, fecha_hora
                        , tlci.`Serial` as serie,
                        id_tecnico_origen
                        , id_tecnico_destino,
                        observacion
                        , tlci.id_movimiento as id_movimiento_actual
                        , A.motivo , ticket
                        , monto_valor
                        , t.id as id_valor_tecnico
                        , CASE
                                when tlm.Semantica is null THEN 1
                                else descuento
                            END AS descuento
                        ,   escaladas.orden , escaladas.rut , escaladas.archivo
                    FROM TB_CIERRE_INVENTARIO_FALTANTE tlci
                    LEFT JOIN
                    (
                    SELECT id, fecha_hora, serie,
                    id_tecnico_origen, id_tecnico_destino,
                    observacion, id_movimiento, motivo , ticket
                    FROM (
                    SELECT id, fecha_hora, serie,
                    id_tecnico_origen, id_tecnico_destino,ticket,
                    observacion, id_movimiento, motivo,
                    ROW_NUMBER() OVER (PARTITION BY serie ORDER BY fecha_hora DESC) AS row_num
                    FROM TB_LOGIS_MOVIMIENTOS
                    ) AS ranked
                    WHERE row_num = 1
                    ) A
                    ON A.serie = tlci.Serial
                    LEFT JOIN  tb_user_tqw t
                    ON tlci.id_tecnico = t.`Nombre_short`
                    LEFT JOIN tp_logis_movimientos tlm
                    ON tlm.id = A.id_movimiento
                    LEFT JOIN tb_logis_rut_orden_form escaladas
                    ON escaladas.id_mov = A.id
                    LEFT JOIN (select Serial from tb_ferret_directa1
                                union all
                                select Serial from tb_logist_bdreversa
                                )
                    disponi
                    ON disponi.`Serial` = tlci.`Serial`
                    WHERE id_tecnico not in ('ANALISIS','DISPONIBLE')
                    and t.id = '$id_usuario'
                     and CASE
                                when tlm.Semantica is null THEN 1
                                else descuento
                            END = 1

    ");

    $inventario = mysqli_fetch_assoc($cierre_inventario);

    ?>

    <!DOCTYPE html>
    <html lang="en">

    <head>

        <!-- Enlace al archivo CSS externo -->
        <link rel="stylesheet" href="css/tecnico_home_logis.css">
       
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
        <meta http-equiv="Pragma" content="no-cache" />
        <meta http-equiv="Expires" content="0" />

        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="APP TQW">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- T\chathe above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

        <meta name="theme-color" content="#0134d4">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">

        <!-- Title -->
        <title>TQW APP - INDICADORES</title>

        <!-- Favicon -->
        <link rel="icon" href="img/core-img/logo_con.ico">
        <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
        <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
        <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
        <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- Style CSS -->
        <link rel="stylesheet" href="style.css">
        <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" integrity="sha384-k6RqeWecC7o/m0Mlqz5pXALC1mNq6NX6bV+9ssDJel3k7R7xAB1zFj2aG/zf7x0" crossorigin="anonymous"> -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
        <!-- Custom Theme CSS -->
        <link rel="stylesheet" href="css/custom-theme.css">
        <link rel="stylesheet" href="css/tecnico_home_logis.css">
        <!-- Web App Manifest -->
        <!-- <link rel="manifest" href="manifest.json"> -->

    </head>

    <body>
        <!-- Preloader -->
        <div id="preloader">
            <div class="spinner-grow text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>

        <!-- Internet Connection Status -->
        <div class="internet-connection-status" id="internetStatus"></div>

        <!-- Dark mode switching -->
        <div class="dark-mode-switching">
            <div class="d-flex w-100 h-100 align-items-center justify-content-center">
                <div class="dark-mode-text text-center">
                    <i class="bi bi-moon"></i>
                    <p class="mb-0">Switching to dark mode</p>
                </div>
                <div class="light-mode-text text-center">
                    <i class="bi bi-brightness-high"></i>
                    <p class="mb-0">Switching to light mode</p>
                </div>
            </div>
        </div>

        <!-- RTL mode switching -->
        <div class="rtl-mode-switching">
            <div class="d-flex w-100 h-100 align-items-center justify-content-center">
                <div class="rtl-mode-text text-center">
                    <i class="bi bi-text-right"></i>
                    <p class="mb-0">Switching to RTL mode</p>
                </div>
                <div class="ltr-mode-text text-center">
                    <i class="bi bi-text-left"></i>
                    <p class="mb-0">Switching to default mode</p>
                </div>
            </div>
        </div>

        <!-- Setting Popup Overlay -->
        <div id="setting-popup-overlay"></div>

        <!-- Header Area -->
        <?php   include('header_tecnico.php') ?>

        <!-- Offcanvas para FORMULARIO DE SOLICITUD DE MATERIALES -->
        <div class="offcanvas offcanvas-end z-index-top" id="FormSolicitud" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">

            <div class="offcanvas-body p-4">
                <h5>Modulo solicitud materiales</h5>

                <form id="myForm">
                    <div class="container-fluid p-0">
                        <div class="row g-2 align-items-end">
                            <!-- Select con búsqueda -->
                            <div class="col-12 col-md-12">
                                <label for="icon_bootstrap" class="form-label">Material solicitado</label>
                                <div class="input-group">
                                    <select class="form-select select2-search" id="familia_select" name="familia_select"
                                        required>
                                        <option value="">Seleccione una familia</option>
                                        <?php
                                            $familias_query = "SELECT DISTINCT desc_familia FROM TP_materiales ORDER BY desc_familia ASC";
                                            $familias = $conex->query($familias_query);
                                            while ($row = $familias->fetch_assoc()) {
                                                echo "<option value='" . htmlspecialchars($row['desc_familia']) . "'>" . htmlspecialchars($row['desc_familia']) . "</option>";
                                            }
                                        ?>
                                    </select>
                                </div>

                                <div class="input-group">
                                    <select class="form-select select2-search" id="icon_bootstrap" name="icon_bootstrap"
                                        required disabled>
                                        <option value="">Seleccione un material</option>
                                    </select>
                                    <button type="button" class="btn btn-outline-secondary" onclick="resetInput()"
                                        title="Limpiar campo">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>

                            </div>
                        </div>
                        <div class="row g-2 align-items-end mt-2">
                            <!-- Input de cantidad -->
                            <div class="col-12 col-md-12">
                                <label for="cantidad" class="form-label">Cantidad</label>
                                <input type="number" class="form-control" id="cantidad" name="cantidad" min="1" max="500"
                                    required>
                            </div>

                            <!-- Botón de agregar -->
                            <div class="col-12 col-md-12 mt-2">
                                <button type="button" id="agregar_al_carrito" class="btn btn-primary w-100 mt-2">
                                    <i class="bi bi-plus-circle"></i> Agregar
                                </button>
                            </div>
                            <div class="col-12 col-md-12">
                                <a class="btn btn-success w-100" onclick="confirmarSolicitud()">
                                    <i class="bi bi-cursor"></i> Confirmar solicitud
                                </a>
                            </div>
                        </div>

                        <hr>

                    </div>
                </form>

                <script>
                    document.getElementById('icon_bootstrap').addEventListener('input', function() {
                        const input = this;
                        const datalist = document.getElementById('icon_autocomplete');
                        const options = datalist.getElementsByTagName('option');
                        let isValid = false;
                        for (let option of options) {
                            if (input.value === option.value) {
                                isValid = true;
                                break;
                            }
                        }
                        if (!isValid) {
                            input.setCustomValidity('Please select a valid option from the list');
                        } else {
                            input.setCustomValidity('');
                        }
                    });

                    function resetInput() {
                        var inputElement = document.getElementById('icon_bootstrap');
                        inputElement.readOnly = false;
                        inputElement.value = '';
                    }
                </script>

                <br>

                <?php if ($id_usuario == 73 || $id_usuario == 160) : ?>
                <hr>
                <h6>ASIGNACIÓN</h6>

                <div class="mb-3" id="tecnicoTraspaso23x">
                    <label for="tecnico_select" class="form-label">Técnico a quien transfiere</label>
                    <select class="form-control" id="tecnicoTraspaso" name="tecnicoTraspaso" required>
                        <option value="">Seleccione el técnico a transferir</option>
                        <?php
                                if ($dotacion->num_rows > 0) {
                                    while ($row = mysqli_fetch_assoc($dotacion)) {
                                        echo "<option value=\"" . $row["id"] . "\">" . $row["nombre"] . "</option>";
                                    }
                                }
                                ?>
                    </select>
                </div>

                <label for="seriadoCarrito" class="form-label">Seriado</label>
                <input type="text" class="form-control" id="seriadoCarrito" name="seriadoCarrito" required>

                <?php endif; ?>

                </form>

                <div id="carrito">
                    <!-- Aquí se mostrarán las selecciones guardadas -->
                </div>

            </div>
        </div>

        <!-- Offcanvas para FORMULARIO PARA INGRESO DE SERIE REVERSA -->
        <div class="offcanvas offcanvas-end" id="MenuSerieReversa" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">
            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <div class="offcanvas-body p-4">
                <h5>Formulario para el ingreso de serie reversa</h5>

                <form id="FormSoporteTecnico">

                    <div class="mb-2">
                        <label for="usuario_origen" class="form-label">Serie</label>
                        <input type="text" class="form-control" id="serieRever_nueva" name="serieRever_nueva" min="1"
                            max="500" required>
                    </div>

                    <div class="mb-2">
                        <label for="usuario_origen" class="form-label">RUT</label>
                        <input type="text" class="form-control" id="rutSerieRev" name="rutSerieRev"
                            pattern="\d{1,2}\.\d{3}\.\d{3}[-][0-9kK]{1}" title="Ingrese RUT en formato XX.XXX.XXX-X"
                            required oninput="formatRut(this)" maxlength="12">
                    </div>
                    <script>
                        function formatRut(input) {
                            let rut = input.value.replace(/[^0-9kK]/g, '');
                            if (rut.length > 1) {
                                rut = rut.slice(0, -1) + '-' + rut.slice(-1);
                            }
                            if (rut.length > 4) {
                                rut = rut.slice(0, -5) + '.' + rut.slice(-5);
                            }
                            if (rut.length > 8) {
                                rut = rut.slice(0, -9) + '.' + rut.slice(-9);
                            }
                            input.value = rut.slice(0, 12);
                        }
                    </script>

                    <div class="mb-2">
                        <label for="usuario_origen" class="form-label">Orden</label>
                        <input type="text" class="form-control" id="ordenSerieRev" name="ordenSerieRev" min="1" max="500"
                            required>
                    </div>

                    <div class="mb-2">
                        <label for="usuario_origen" class="form-label">Observación</label>
                        <textarea class="form-control" id="obs_serie" name="obs_serie" rows="4"></textarea>
                    </div>

                    <a class="btn m-1 btn-info" onclick="enviarDatos()">
                        <i class="bi bi-cursor"></i> Ingresar serie
                    </a>
                </form>
            </div>
        </div>

        <!-- Offcanvas para FORMULARIO PARA INGRESO DE SERIE REVERSA -->
        <div class="offcanvas offcanvas-end" id="soporteTecnico" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">
            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <div class="offcanvas-body p-4">
                <h5>Si tienes un problema con tu app , registralo aca</h5>

                <form id="FormSoporteTecnico">

                    <!-- <div class="mb-2">
                            <label for="usuario_origen" class="form-label">Serie</label>
                            <input type="text" class="form-control" id="serieRever_nueva" name="serieRever_nueva" min="1"
                                max="500" required>
                        </div> -->

                    <div class="mb-2">
                        <label for="modulo" class="form-label">Pool afectado</label>
                        <select class="form-select" id="soporte_modulo" name="soporte_modulo" required>
                            <option value="" selected disabled>Seleccione un pool</option>
                            <option value="Pool Directa">Pool Directa</option>
                            <option value="Pool Reversa">Pool Reversa</option>
                            <option value="Pool Asignación">Pool Asignación</option>
                            <option value="Faltante inventario">Faltante inventario</option>
                            <option value="Pool Solicitud material">Pool Solicitud material</option>
                            <option value="Pool Ingreso reversa manual">Pool Ingreso reversa manual</option>
                        </select>
                    </div>

                    <div class="mb-2">
                        <label for="observaciones" class="form-label">Observaciones</label>
                        <textarea class="form-control" id="soporte_observaciones" name="soporte_observaciones" rows="4"
                            required></textarea>
                    </div>

                    <div class="mb-2">
                        <label for="complemento" class="form-label">Complemento</label>
                        <textarea class="form-control" id="soporte_complemento" name="soporte_complemento" rows="4"
                            required></textarea>
                    </div>

                    <a class="btn m-1 btn-info" onclick="enviarDatosSoporte()">
                        <i class="bi bi-cursor"></i> Ingresar observación
                    </a>
                </form>
            </div>
        </div>

        <script>
            function enviarDatos() {
                // Obtener los valores de los campos del formulario
                const serieReverNueva = document.getElementById('serieRever_nueva').value;
                const rutSerieRev = document.getElementById('rutSerieRev').value;
                const ordenSerieRev = document.getElementById('ordenSerieRev').value;
                const obs_serie = document.getElementById('obs_serie').value;
                const locator = '<?php echo $usuario; ?>';
                const id_tecnico = '<?php echo $id_usuario; ?>';
                // Verificar si serieRever_nueva tiene un valor
                if (!serieReverNueva) {
                    alert('Por favor, ingrese un valor para la serie reversa.');
                    return;
                }
                // Crear un objeto con los datos
                const data = {
                    serieRever_nueva: serieReverNueva,
                    rutSerieRev: rutSerieRev,
                    ordenSerieRev: ordenSerieRev,
                    locator: locator,
                    obs_serie: obs_serie,
                    id_tecnico: id_tecnico
                };
                // Imprimir los datos en la consola
                console.log("Datos enviados: ", JSON.stringify(data));
                // Crear un objeto FormData
                const formData = new FormData();
                formData.append('serieRever_nueva', serieReverNueva);
                formData.append('rutSerieRev', rutSerieRev);
                formData.append('ordenSerieRev', ordenSerieRev);
                formData.append('locator', locator);
                formData.append('obs_serie', obs_serie);
                formData.append('id_tecnico', id_tecnico);
                // Configurar la solicitud AJAX
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'POST_API.php?proceso=insertSerieNueva', true);
                // Evento para manejar la respuesta del servidor
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        // Aquí puedes manejar la respuesta del servidor
                        console.log(xhr.responseText);
                        // Mostrar un popup con mensaje de éxito
                        alert(xhr.responseText);
                        // Limpiar los campos del formulario
                        document.getElementById('serieRever_nueva').value = "";
                        document.getElementById('rutSerieRev').value = "";
                        document.getElementById('ordenSerieRev').value = "";
                        document.getElementById('obs_serie').value = "";
                        // Recargar la página actual
                        location.reload();
                    }
                };
                // Enviar la solicitud con los datos del formulario
                xhr.send(formData);
            }
        </script>

        <script>
            function enviarDatosSoporte() {
                // Obtener los valores de los campos del formulario
                const soporte_observaciones = document.getElementById('soporte_observaciones').value;
                const soporte_modulo = document.getElementById('soporte_modulo').value;
                const soporte_complemento = document.getElementById('soporte_complemento').value;
                const id_tecnico = '<?php echo $id_usuario; ?>';
                // Crear un objeto con los datos
                const data = {
                    soporte_observaciones: soporte_observaciones,
                    soporte_modulo: soporte_modulo,
                    soporte_complemento: soporte_complemento,
                    id_tecnico: id_tecnico
                };
                // Imprimir los datos en la consola
                console.log("Datos enviados: ", JSON.stringify(data));
                // Crear un objeto FormData
                const formData = new FormData();
                formData.append('soporte_observaciones', soporte_observaciones);
                formData.append('soporte_modulo', soporte_modulo);
                formData.append('soporte_complemento', soporte_complemento);
                formData.append('id_tecnico', id_tecnico);
                // Configurar la solicitud AJAX
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'POST_API.php?proceso=insertSoporteTenico', true);
                // Evento para manejar la respuesta del servidor
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        // Aquí puedes manejar la respuesta del servidor
                        console.log(xhr.responseText);
                        // Mostrar un popup con mensaje de éxito
                        alert(xhr.responseText);
                        // Limpiar los campos del formulario
                        document.getElementById('soporte_observaciones').value = "";
                        document.getElementById('soporte_modulo').value = "";
                        document.getElementById('soporte_complemento').value = "";
                        // Recargar la página actual
                        location.reload();
                    }
                };
                // Enviar la solicitud con los datos del formulario
                xhr.send(formData);
            }
        </script>

        <div class="offcanvas offcanvas-start" id="affanOffcanvas" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">

            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <div class="offcanvas-body p-0">
                <div class="sidenav-wrapper">
                    <!-- Sidenav Profile -->
                    <div class="sidenav-profile bg-gradient">
                        <div class="sidenav-style1"></div>

                        <div class="user-info">
                            <h6 class="user-name mb-0">Bienvenido</h6>
                            <span style="color: white;">

                                <?php echo $nombre; ?>

                            </span>
                        </div>

                        <!-- User Info -->

                    </div>

                    <!-- Sidenav Nav -->
                    <ul class="sidenav-nav ps-0">
                        <li>
                            <a href="home.php?usuario=<?php echo $usuario; ?>"><i class="bi bi-house-door"></i>HOME</a>
                        </li>

                        <li>
                            <a href="Tecnico_calidad.php?usuario=<?php echo $usuario; ?>"><i
                                    class="bi bi-collection"></i>CALIDAD
                                REACTIVA

                                <span class="badge bg-success rounded-pill ms-2"></span>
                            </a>
                        </li>

                        <li>
                            <a href="Tecnico_DeclaracionOT.php?usuario=<?php echo $usuario; ?>"><i
                                    class="bi bi-collection"></i>DECLARACION DE ORDENES
                                <span class="badge bg-success rounded-pill ms-2"></span>
                            </a>
                        </li>

                        <li>
                            <a href="#" class="nav-url"><i class="bi bi-globe"></i>ACCESOS DIRECTOS<span
                                    class="dropdown-icon"></span></a>
                            <ul>
                                <li>
                                    <a
                                        href="https://app.powerbi.com/view?r=eyJrIjoiNWJjY2UwMGUtYzk3My00OGRmLTgwMTUtZGUzZDI0OGQ0NTJlIiwidCI6ImE0ZDNhYWYwLWJlZjAtNDAzMS1iZGQ3LTM1MzZkYTFmMjQ2ZCJ9"><i
                                            class="bi bi-globe"></i>Power BI LOGISTICA</a>
                                </li>

                                <li>
                                    <a href="https://entrenamientovirtual.cl/course/"><i class="bi bi-globe"></i>Desafio
                                        Tecnico
                                        <span class="badge bg-success rounded-pill ms-2"></span>
                                    </a>
                                </li>
                                <li>
                                    <a href="http://172.17.113.6/eps/index.do"><i class="bi bi-globe"></i>NDC Declaracion
                                        Material
                                        <span class="badge bg-success rounded-pill ms-2"></span>
                                    </a>
                                </li>
                                <li>
                                    <a href="https://forms.gle/3m3ZUDby4ie5Y5Us7"><i class="bi bi-globe"></i>Registro Serie
                                        instaladas
                                        <span class="badge bg-success rounded-pill ms-2"></span>
                                    </a>
                                </li>
                                <li>
                                    <a href="https://lla.cloudcheck.net/t1gui/login-page"><i
                                            class="bi bi-globe"></i>Cloudcheck
                                        <span class="badge bg-success rounded-pill ms-2"></span>
                                    </a>
                                </li>
                            </ul>
                        </li>

                        <li>
                            <div class="night-mode-nav">
                                <i class="bi bi-moon"></i>CAMBIAR MODO OSCURO
                                <div class="form-check form-switch">
                                    <input class="form-check-input form-check-success" id="darkSwitch" type="checkbox">
                                </div>
                            </div>
                        </li>
                        <li>
                            <a href="login.php"><i class="bi bi-box-arrow-right"></i>CERRAR SESIÓN</a>
                        </li>
                    </ul>
                    <!-- Social Info -->

                </div>
            </div>
        </div>


        <div id="spinner" style="display:none;" class="spinner">
        </div>

        <div id="dark-fondo" class="fondo-oscuro" style="display:none;"></div>

        <div style="height:50px" class="container">
        </div>
        <div class="container">
            <div class="affan-element-item">
                <div class="element-heading-wrapper">
                    <i class="bi bi-bar-chart-fill"></i>
                    <div class="heading-text">
                        <h6 class="mb-1">RESUMEN DE INDICADORES</h6>
                        <span>Detalle de los materiales asignados desde bodega </span>
                    </div>
                </div>
            </div>
        </div>
        <!-- HTML RESUMEN DE INDICADORES  -->
        <div class="container">
            <div class="card">
                <div class="card-body direction-rtl">
                    <div class="row">

                        <div class="col-4">
                            <!-- Single Counter -->
                            <div class="single-counter-wrap text-center">
                                <i class="bi-bar-chart-line mb-2 text-success"></i>
                                <h4 class="mb-0">
                                    <span class="counter">
                                        <?php
                                        // echo round($indicadores_row['Total Directa'], 1);
                                        ?>
                                    </span>
                                </h4>
                                <span class="solid-line"></span>
                                <p class="mb-0 fz-12">TOTAL DIRECTA</p>
                            </div>
                        </div>

                        <div class="col-4">
                            <!-- Single Counter -->
                            <div class="single-counter-wrap text-center">
                                <i class="bi-bar-chart-line mb-2 text-success"></i>
                                <h4 class="mb-0">
                                    <span class="counter">
                                        <?php
                                        // echo round($indicadores_row['Registros Disponible'], 1);
                                        ?> </span>
                                </h4>
                                <span class="solid-line"></span>
                                <p class="mb-0 fz-12">DISPONIBLES</p>
                            </div>
                        </div>

                        <div class="col-4">
                            <!-- Single Counter -->
                            <div class="single-counter-wrap text-center">
                                <i class="bi-bar-chart-line mb-2 text-success"></i>
                                <h4 class="mb-0">
                                    <span class="counter">
                                        <?php
                                        // echo round($indicadores_row['Registros instaladas'], 1);
                                        ?>
                                    </span>
                                </h4>
                                <span class="solid-line"></span>
                                <p class="mb-0 fz-12">INSTALADA</p>
                            </div>
                        </div>

                        <div class="col-4">
                            <!-- Single Counter -->
                            <div class="single-counter-wrap text-center">
                                <i class="bi-bar-chart-line mb-2 text-warning"></i>
                                <h4 class="mb-0">
                                    <span class="counter">
                                        <?php
                                        // echo round($indicadores_row['Total REVERSA'], 1);
                                        ?>
                                    </span>
                                </h4>
                                <span class="solid-line"></span>
                                <p class="mb-0 fz-12">TOTAL REVERSA</p>
                            </div>
                        </div>

                        <div class="col-4">
                            <!-- Single Counter -->
                            <div class="single-counter-wrap text-center">
                                <i class="bi-bar-chart-line mb-2 text-warning"></i>
                                <h4 class="mb-0">
                                    <span class="counter">
                                        <?php
                                        //  echo round($indicadores_row['Entregado'], 1);
                                        ?>
                                    </span>
                                </h4>
                                <span class="solid-line"></span>
                                <p class="mb-0 fz-12">ENTREGADAS</p>
                            </div>
                        </div>

                        <div class="col-4">
                            <!-- Single Counter -->
                            <div class="single-counter-wrap text-center">
                                <i class="bi-bar-chart-line mb-2 text-warning"></i>
                                <h4 class="mb-0">
                                    <span class="counter">
                                        <?php
                                        // echo round($indicadores_row['Disponible'], 1);
                                        ?>
                                    </span>
                                </h4>
                                <span class="solid-line"></span>
                                <p class="mb-0 fz-12">JUSTIFICADOS</p>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>

        <!-- PANEL DE INDICADORES  ---- -- -- -- -- -- -- -- --  -->

        <div class="page-content-wrapper py-3">
            <!-- Element Heading -->
            <!-- <div class="container mb-4">
                <div class="card">
                    <div class="element-heading d-flex justify-content-start align-items-center">
                        <h6 class="mb-0 me-2">Última actualización</h6>
                    </div>
                </div>
            </div> -->

            <div class="container">
                <div class="affan-element-item">
                    <div class="element-heading-wrapper">
                        <i class="bi bi-clock fs-3"></i>
                        <div class="heading-text">
                            <h4 class="mb-1">Última actualización</h4>

                        </div>
                    </div>
                </div>
            </div>

            <div class="container">
                <div class="card">
                    <!-- <div class="card-body"> -->
                    <div class="standard-tab">
                        <ul class="nav rounded-lg mb-2 p-2 shadow-sm" id="affanTabs1" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="btn active" id="bootstrap-tab" data-bs-toggle="tab"
                                    data-bs-target="#bootstrap" type="button" role="tab" aria-controls="bootstrap"
                                    aria-selected="true">Faltante</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="btn" id="pwa-tab" data-bs-toggle="tab" data-bs-target="#pwa" type="button"
                                    role="tab" aria-controls="pwa" aria-selected="false">Recepcionar</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="btn" id="dark-tab" data-bs-toggle="tab" data-bs-target="#dark" type="button"
                                    role="tab" aria-controls="dark" aria-selected="false">Directa</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="btn" id="rever-tab" data-bs-toggle="tab" data-bs-target="#rever"
                                    type="button" role="tab" aria-controls="rever" aria-selected="false">Reversa</button>
                            </li>
                        </ul>

                        <div class="tab-content rounded-lg p-3 shadow-sm" id="affanTabs1Content">
                            <div class="tab-pane fade show active" id="bootstrap" role="tabpanel"
                                aria-labelledby="bootstrap-tab">

                                <div class="container">
                                    <div class="affan-element-item">
                                        <div class="element-heading-wrapper">
                                            <i class="bi bi-house-exclamation-fill"></i>
                                            <div class="heading-text">
                                                <h6 class="mb-1">FALTANTE DE INVENTARIO </h6>
                                                <span>Materiales faltantes de procesos inventario con plazo de justificación
                                                    en 72 horas a partir de la fecha del inventario correspondiente </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="container" style=" border-radius: 10px; /* Radio de borde redondeado */
                                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Sombra suave */
                                        background-color: #f4f4f4; /* Color de fondo, puedes ajustarlo */
                                        padding: 20px; /* Espaciado interno, puedes ajustarlo */
                                        ">

                                    <div class="card-body direction-rtl" style="padding:0rem">
                                        <div class="row">

                                            <?php
                                            echo '<input type="text" data-table="tablaCierreInventario" id="searchInputtablaCierreInventario" name="search-box" placeholder="Buscar...">';
                                            echo '</input>';
                                            // Genera el código HTML de la tabla
                                            echo '<table id="tablaCierreInventario">';
                                            echo '<thead>';
                                            echo '<tr>';
                                            echo '<th>Serial</th>';
                                            echo '<th>Origen</th>';
                                            echo '<th>Estado</th>';
                                            echo '<th>Justificar</th>';
                                            echo '</tr>';
                                            echo '</thead>';
                                            echo '<tbody>';
                                            $counter = 0;
                                            while ($fila = mysqli_fetch_assoc($cierre_inventario)) {
                                                $counter++;
                                                echo '<tr>';
                                                echo '<td class="button-cell ajustado">' . $fila['Serial'] . '</td>';
                                                echo '<td class="button-cell">' . $fila['flujo'] . '</td>';
                                                echo '<td class="button-cell">' . $fila['Semantica'] . '</td>';
                                                echo '<td class="estado-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Serial'] . '\', \'' . $fila['Serial'] . '\', \'TRANSFIERE\')"><i class="bi bi-send-fill"></i></button></td>';
                                                echo '</tr>';
                                            }
                                            echo "Total registros: " . $counter;
                                            echo '</tbody>';
                                            echo '</table>';

                                            ?>

                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="pwa" role="tabpanel" aria-labelledby="pwa-tab">
                                <div class="container">
                                    <div class="affan-element-item">
                                        <div class="element-heading-wrapper">
                                            <i class="bi bi-list-task"></i>
                                            <div class="heading-text">
                                                <h6 class="mb-1">POOL DE MATERIAL POR RECEPCIONAR</h6>
                                                <span>Detalle de los materiales asignados desde bodega , tecnicos o
                                                    supervisores </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-----------BOTON MASIVO--------->
                                <div class="container">
                                    <!-- Botón fuera de la tabla -->
                                    <button id="aceptarMasivo" class="btn btn-success">Aceptar Masivo</button>
                                </div>
                                <!-----------BOTON --------->
                                <div class="container" style=" border-radius: 10px; /* Radio de borde redondeado */
                                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Sombra suave */
                                        background-color: #f4f4f4; /* Color de fondo, puedes ajustarlo */
                                        padding: 20px; /* Espaciado interno, puedes ajustarlo */
                                        ">

                                    <div class="card-body direction-rtl" style="padding:0rem">
                                        <div class="row">

                                            <?php
                                            // Genera el código HTML de la tabla
                                            echo '<input type="text" data-table="tablaAsignacion" id="searchInputtablaAsignacion" name="search-box" placeholder="Buscar...">';
                                            echo '</input>';

                                            echo '<table id="tablaAsignacion">';

                                            // Establecer head de la tabla
                                            echo '<thead>';
                                            echo '<tr><th>Status</th><th>Serial</th><th>Estado</th><th>VER</th><th>Acepta</th><th>Rechaza</th></tr>';
                                            echo '</thead>';

                                            // Abrir cuerpo de la tabla
                                            echo '<tbody>';
                                            if ($asignacion->num_rows > 0) {
                                                while ($fila = mysqli_fetch_assoc($asignacion)) {
                                                    $variableCondicion = ($fila['id_movimiento'] == 15) ? "Si" : $fila['id_tecnico_destino'];

                                                    echo '<tr>';
                                                    echo '<td sty   le="font-size: 12px;">';
                                                    if ($fila['Job'] == '0') {
                                                        echo '<span style="display:inline-block; width:12px; height:14px; background-color:green; border-radius:50%;"></span>';
                                                    } else {
                                                        echo '<span style="display:inline-block; width:12px; height:14px; background-color:red; border-radius:50%;"></span>';
                                                    }
                                                    echo '</td>';
                                                    echo '<td class="estado-cell ajustado">' . $fila['Serial'] . '</td>';
                                                    echo '<td class="estado-cell">' . $fila['Semantica'] . '</td>';
                                                    echo '<td class="button-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'VER\')"><i class="bi bi-eye-fill"></i></button></td>';
                                                    echo '<td class="button-cell"><button type="button" class="btn btn-success" onclick="actualizarRegistro(\'' . $fila['Serial'] . '\', \'' . $variableCondicion . '\', \'' . $fila['id_movimiento'] . '\', \'ACEPTA\')"><i class="bi bi-check-circle"></i></button></td>';
                                                    echo '<td class="button-cell"><button type="button" class="btn btn-danger" onclick="rechazoMaterial(\'' . $fila['Serial'] . '\', \'' . $fila['id_transfer'] . '\', \'' . $fila['Item'] . '\', \'RECHAZA\')"><i class="bi bi-journal-x"></i></button></td>';
                                                    echo '</tr>';
                                                }
                                            } else {
                                                echo '<tr>';
                                                echo '<td colspan="6" class="text-center">';
                                                echo '<div class="alert alert-info" role="alert">';
                                                echo '<i class="bi bi-info-circle"></i> No hay registros disponibles';
                                                echo '</div>';
                                                echo '</td>';
                                                echo '</tr>';
                                            }

                                            echo '</tbody>';
                                            echo '</table>';
                                            ?>

                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="tab-pane fade" id="dark" role="tabpanel" aria-labelledby="dark-tab">
                                <div class="container">
                                    <div class="affan-element-item">
                                        <div class="element-heading-wrapper">
                                            <i class="bi bi-car-front-fill"></i>
                                            <div class="heading-text">
                                                <h6 class="mb-1">POOL DIRECTA</h6>
                                                <span>Según oracle </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- <div class="container">
                                    <button id="openModalBtn" type="button" class="btn btn-primary">
                                        <i class="bi bi-send"></i> Envío a bodega
                                    </button>
                                </div> -->
                                <div class="container" style="border-radius: 10px; /* Radio de borde redondeado */
                                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Sombra suave */
                                    background-color: #f4f4f4; /* Color de fondo, puedes ajustarlo */
                                    padding: 20px; /* Espaciado interno, puedes ajustarlo */    ">

                                    <div class="card-body direction-rtl" style="padding:0rem">
                                        <div class="row">

                                        <?php

                                             // Genera el código HTML de la tabla
                                             echo '<input type="text" data-table="TablaDirecta" id="searchInputTablaDirecta" name="search-box" placeholder="Buscar...">';
                                             echo '</input>';

                                            echo '<table id="TablaDirecta">';
                                            echo '<thead>';  // Añade thead para mejor estructura
                                            echo '<tr><th>Serial</th><th>Estado</th><th>Ver</th><th>Declarar</th><th>Transfiere</th></tr>';
                                            echo '</thead>';
                                            echo '<tbody>';  // Añade tbody para mejor estructura

                                            while ($fila = mysqli_fetch_assoc($stock)) {
                                                echo '<tr>';  // Mover apertura de tr aquí
                                                echo '<td class="estado-cell">' . $fila['Serial'] . '</td>';
                                                echo '<td class="estado-cell ' . strtolower($fila['Semantica']) . '">' . $fila['Semantica'] . '</td>';
                                                echo '<td class="button-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'VER\')"><i class="bi bi-eye-fill"></i></button></td>';
                                                echo '<td class="estado-cell"><button type="button" class="btn btn-success" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'INSTALA\')"><i class="bi bi-house-check-fill"></i></button></td>';
                                                echo '<td class="estado-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'TRANSFIERE\')"><i class="bi bi-send-fill"></i></button></td>';
                                                echo '</tr>';
                                            }

                                            echo '</tbody>';
                                            echo '</table>';
                                        ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="rever" role="tabpanel" aria-labelledby="rever-tab">

                                <div class="container">
                                    <div class="affan-element-item">
                                        <div class="element-heading-wrapper">
                                            <i class="bi bi-house-dash"></i>
                                            <div class="heading-text">
                                                <h6 class="mb-1">POOL REVERSA</h6>
                                                <span>Según oracle </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="container" style="border-radius: 10px; /* Radio de borde redondeado */
                                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Sombra suave */
                                        background-color: #f4f4f4; /* Color de fondo, puedes ajustarlo */
                                        padding: 20px; /* Espaciado interno, puedes ajustarlo */    ">

                                    <!-- <div class="card"> -->
                                    <div class="card-body direction-rtl" style="padding:0rem">
                                        <div class="row">

                                            <?php
                                            // Realiza la consulta a la base de datos y obtén los resultados

                                            // Genera el código HTML de la tabla con header y cuerpo
                                            echo '<input type="text" data-table="poolBodegaReversa" id="searchInputpoolBodegaReversa" name="search-box" placeholder="Buscar...">';
                                            echo '</input>';
                                            echo '<table id="poolBodegaReversa" style="font-size: 12px;">';

                                            echo '<thead>';
                                            echo '<tr><th>Serial</th><th>Estado</th><th>Historial</th><th>Declarar</th><th>A supervisor</th></tr>';
                                            echo '</thead>';

                                            echo '<tbody>';
                                            while ($fila = mysqli_fetch_assoc($bd_reversa)) {
                                                echo '<tr>';
                                                echo '<td class="button-cell">' . $fila['Serial'] . '</td>';
                                                echo '<td class="button-cell">' . $fila['Semantica'] . '</td>';
                                                echo '<td class="button-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', ' . $id_usuario . ', \'VER\')"><i class="bi bi-eye-fill"></i></button></td>';
                                                echo '<td class="button-cell"><button type="button" class="btn btn-success" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'ENTREGA_REV\')"><i class="bi bi-house-check-fill"></i></button></td>';
                                                echo '<td class="button-cell"><button type="button" class="btn btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', \'' . $fila['id_movimiento'] . '\', \'TRANSFIERE_REV\')"><i class="bi bi-send-fill"></i></button></td>';
                                                echo '</tr>';
                                            }
                                            echo '</tbody>';
                                            echo '</table>';
                                            ?>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- </div> -->
                </div>
            </div>

            <div id="modal_dev_bodega" class="modal">
                <form id="formulario_check" method="post" action="POST_API.php">
                    <input type="hidden" name="proceso" value="insert_serie_a_bodega">
                    <input type="hidden" name="id_sesion" value="<?php echo $sesion; ?>">
                    <input type="hidden" name="id_usuario" value="<?php echo $id_usuario; ?>">
                    <div class="modal-content">
                        <span class="close">&times;</span>
                        <table id="dataTable_bodega">
                            <thead>
                                <tr>

                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                        <div class="row text-center" style="margin-top: 10px;">
                            <button type="submit" name="enviar" class="btn btn-primary">
                                <i class="bi bi-check"></i> Enviar a bodega
                            </button>
                            <button type="button" class="btn btn-secondary">
                                <i class="bi bi-x"></i> Cancelar
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!----------- POP UP COMBO - RECHAZO TECNICO --------->
            <div id="popup-container" style="display: none;  justify-content: center; align-items: center; height: 100vh;">
                <div id="popup" style="text-align: center;">
                    <h2
                        style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', sans-serif; font-weight: 500; color: #333; margin-bottom: 20px;">
                        Gestión de rechazo</h2>
                    <input type="hidden" id="popupSerial">
                    <input type="hidden" id="popupTicket">
                    <input type="hidden" id="popupIdTecnicoDestino">
                    <input type="hidden" id="popupAccion">

                    <select id="motivoRechazo" style="margin: 10px auto; display: block;">
                        <option value="Serie fisica no entregada">Serie fisica no entregada</option>
                        <option value="Serie no corresponde">Serie no corresponde </option>
                    </select>
                    <button class="btn btn-warning apple-style-button" onclick="Rechazoaceptar()"
                        style="margin: 5px;">Aceptar</button>
                    <button class="btn btn-warning apple-style-button" onclick="Rechazocancelar()"
                        style="margin: 5px;">Cancelar</button>
                </div>
            </div>

            <!----------------------------------------------------------------------------------------------------
    -------------->
            <script>
                function mostrarPopup(Serial, ticket, id_tecnico_destino, accion) {
                    // Mostrar el cuadro de diálogo
                    document.getElementById('popup-container').style.display = 'block';
                    // Configura los valores necesarios para la función rechazoMaterial
                    document.getElementById('popupSerial').value = Serial;
                    document.getElementById('popupTicket').value = ticket;
                    document.getElementById('popupIdTecnicoDestino').value = id_tecnico_destino;
                    document.getElementById('popupAccion').value = accion;
                }
            </script>

            <!----------------------------------------------------------------------------------------------------
    ------------------------------------------- TABLA DE DIRECTA -----------------------------------------
    ------------------------------------------------------------------------------------------------------>

            <!--------------------------------------------------------------------------------------
    ------------------------------------------- TABLA DE REVERSA ---------------------------
    ---------------------------------------------------------------------------------------->

        </div>

        <!----------------------------------------------------------------------------------------------------
    -------------------------------------------OFFCANVAS DE LA PAGINA --------------------------------------
    --------------------------------------------------------------------------------------

    Offcanvas NUEVO -->
        <div class="offcanvas offcanvas-end" id="offcanvasNuevo" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="nuevoOffcanvasLabel">
            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>
            <div class="offcanvas-body p-4">
                <h5>Título del nuevo menú lateral</h5>
                <form method="POST">
                    <!-- Aquí van los campos del formulario -->
                    <button type="button" class="btn btn-success" id="nuevoButton">Acción</button>
                </form>
            </div>
        </div>

        <!--   Offcanvas IZQUIERDO -->
        <div class="offcanvas offcanvas-start" id="offcanvasleft_PANEL" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">

            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <!-- Offcanvas Body with Form -->
            <div class="offcanvas-body p-4">
                <h5>FORMULARIO DE MOVIMIENTO</h5>

                <div class="mb-3">
                    <label for="icon_bootstrap" class="form-label">LA SERIE A ASIGNAR</label>
                    <input type="text" class="form-control" id="serieInput" name="serieInput" value="" readonly>
                </div>

                <form id="confirmaForm" action="Action_LOGIS_API.php?process=confirma&id_sesion=<?php echo $sesion; ?>"
                    method="POST">

                    <div class="mb-3">
                        <!-- <label for="icon_bootstr" class="form-label">LA SERIE A ASIGNAR</label> -->
                        <input type="hidden" id="serieInput2" name="serieInput2" value="" readonly>
                    </div>

                    <button type="button" class="btn m-1 btn-success" id="confirmaButton">
                        <i class="bi bi-arrow-repeat"></i>Acepta asignación
                    </button>
                </form>

                <form id="rechazaForm" action="Action_LOGIS_API.php?process=rechaza&id_sesion=<?php echo $sesion; ?>"
                    method="POST">
                    <div class="mb-3">
                        <!-- <label for="icon_bootstrap" class="form-label">LA SERIE A ASIGNAR</label> -->
                        <input type="hidden" id="serieInput2" name="serieInput2" value="" readonly>
                    </div>

                    <button type="button" class="btn m-1 btn-danger" id="rechazaButton">
                        <i class="bi bi-arrow-repeat"></i>Rechaza asignación
                    </button>
                </form>

                <form id="traspasaForm" action="Action_LOGIS_API.php?process=traspasa&id_sesion=<?php echo $sesion; ?>"
                    method="POST">
                    <div class="mb-3">
                        <!-- <label for="icon_bootstrap" class="form-label">LA SERIE A ASIGNAR</label> -->
                        <input type="hidden" id="serieInput2" name="serieInput2" value="" readonly>
                    </div>

                    <button type="button" class="btn m-1 btn-warning" id="traspasaButton">
                        <i class="bi bi-arrow-repeat"></i>Traspaso entre tecnicos
                    </button>
                </form>

                <form id="justificaForm" action="Action_LOGIS_API.php?process=justifica&id_sesion=<?php echo $sesion; ?>"
                    method="POST">
                    <div class="mb-3">
                        <!-- <label for="icon_bootstrap" class="form-label">LA SERIE A ASIGNAR</label> -->
                        <input type="hidden" id="serieInput2" name="serieInput2" value="" readonly>
                    </div>

                    <button type="button" class="btn m-1 btn-warning" id="justificaButton">
                        <i class="bi bi-arrow-repeat"></i>Justificación
                    </button>
                </form>

                <form id="instalaForm" action="Action_LOGIS_API.php?process=instala&id_sesion=<?php echo $sesion; ?>"
                    method="POST">
                    <div class="mb-3">
                        <!-- <label for="icon_bootstrap" class="form-label">LA SERIE A ASIGNAR</label> -->
                        <input type="hidden" id="serieInput2" name="serieInput2" value="" readonly>
                    </div>

                    <button type="button" class="btn m-1 btn-success" id="instalaButton">
                        <i class="bi bi-arrow-repeat"></i>Declara instalada
                    </button>
                </form>
            </div>
        </div>

        <!-- Offcanvas TRANSFERENCIA  -->
        <div class="offcanvas offcanvas-end" id="offcanvasrigh" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel" style="z-index: 1045;">
            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <div class="offcanvas-body p-4">
                <h5>Canal de requerimientos</h5>

                <div class="mb-3">
                    <label for="serie_tran" class="form-label">SERIE SELECCIONADA</label>
                    <input type="text" class="form-control" id="serie_tran" name="serie_tran" value="" readonly required>
                </div>

                <h5>ESCALAMIENTO A SUPERVISOR</h5>

                <a class="btn m-1 btn-info" href="#" id="justificarLink">
                    <i class="bi bi-cursor"></i> Validación supervisor
                </a>

                <a class="btn m-1 btn-info" href="#" id="bodegaSistemico">
                    <i class="bi bi-cursor"></i> PROBLEMA SISTEMICO
                </a>

                <a class="btn m-1 btn-info" href="#" id="bodegaSeriIncorrecta">
                    <i class="bi bi-cursor"></i> Equipo serie incorrecta
                </a>

                <h5 style="MARGIN-TOP: 15px;"> ESCALAMIENTO A BODEGA </h5>

                <div class="d-flex flex-column gap-2">

                    <a class="btn btn-danger text-wrap d-flex align-items-center gap-2" href="#" id="bodegaLink">
                        <i class="bi bi-cursor"></i>
                        <span>Equipo con desperfecto</span>
                    </a>

                    <a class="btn btn-danger text-wrap d-flex align-items-center gap-2" href="#" id="bodegaLinkTOA">
                        <i class="bi bi-cursor"></i>
                        <span>Serie no aparece en TOA</span>
                    </a>

                    <a class="btn btn-danger text-wrap d-flex align-items-center gap-2" href="#" id="fotoCierreInv">
                        <i class="bi bi-cursor"></i>
                        <span>Serie a regularizar por cierre de inventario</span>
                    </a>

                    <a class="btn btn-danger text-wrap d-flex align-items-center gap-2" href="#" id="DevueltoBodega">
                        <i class="bi bi-cursor"></i>
                        <span>Devuelto a bodega</span>
                    </a>

                </div>

                <h5 style="MARGIN-TOP: 15px;"> TRANSFERENCIA ENTRE TECNICOS </h5>

                <a class="btn m-1 btn-warning" style="margin-bottom:10px;margin-top:10px;" href="#" id="tecnicoLink">
                    <i class="bi bi-cursor"></i> Transferencia a otro tecnico
                </a>

                <!-- Etiqueta hr como separador -->
                <hr class="separador">
                <!-- Puedes agregar más campos según los campos de tu tabla TB_NOTIFICACIONES -->

                <div class="mb-3" id="tecnicoTransf" style="display: none;">
    <label for="tecnico_select" class="form-label">Técnico a quien transfiere</label>
    <select class="form-control" id="usuario_destino" name="usuario_destino" required>
        <option value="">Seleccione el técnico a transferir</option>
        <?php
        // Evaluar el perfil y usar la query correspondiente
        $query_to_use = ($perfil == 'user_QA') ? $dotacion_qa : $dotacion2;

        if ($query_to_use->num_rows > 0) {
            while ($row = mysqli_fetch_assoc($query_to_use)) {
                echo "<option value=\"" . $row["id"] . "\">" . $row["nombre"] . "</option>";
            }
        }
        ?>
    </select>
</div>

                <input type="hidden" id="usuario_destino_hidden" name="usuario_destino_hidden">

                <div class="mb-3" id="serie_tran_contain" style="display: none;">
                    <label for="serie_wrong_directa" class="form-label">INGRESE LA SERIE</label>
                    <input type="text" class="form-control" id="serie_tran_new" name="serie_tran_new" value="" required>
                </div>

                <div id="divArchivo" class="form-group" style="display: none;">
                    <label class="form-label" for="customFile3">Cargar Foto</label>
                    <input class="form-control border-0" name="fileInventario" id="fileInventario" type="file">
                </div>

                <div class="mb-3" id="motivo_tran_contain" style="display: none;">
                    <label id="serie_incorrecta_directa" for="motivo_tran" class="form-label">MOTIVO</label>
                    <!-- Cambiar el <input> por <textarea> -->
                    <textarea class="form-control" id="motivo_tran" name="motivo_tran" rows="4" required
                        readonly></textarea>
                </div>

                <!-- Agregar el combo box -->
                <div class="mb-3" id="motivoASuper" style="display: none;">
                    <label class="form-label" for="defaultSelectSm">Seleccionar motivo</label>
                    <select class="form-select form-select-sm form-control-clicked" id="defaultSelectSm"
                        name="defaultSelectSm" aria-label="Default select example">
                        <option value="" selected>SELECCIONAR</option>
                        <option value="perdidaTecnico">PERDIDA MATERIAL POR TECNICO</option>
                        <option value="robo">ROBO</option>

                    </select>
                </div>
                <div class="mb-3">
                    <input type="hidden" class="form-control" id="val_tecnico_destino" name="val_tecnico_destino">
                </div>

                <button type="button" class="btn btn-success" id="transferButton">Enviar requerimiento</button>

            </div>
        </div>

        <!-- Offcanvas INSTALACION de serie -->
        <div class="offcanvas offcanvas-end" id="offcanvasInstala" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">

            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <!-- Offcanvas Body with Form -->
            <div class="offcanvas-body p-4">
                <h5>declara la serie instalada</h5>

                <form method="POST">

                    <div class="mb-3">
                        <label for="serie_insta" class="form-label">SERIE SELECCIONADA</label>
                        <input type="text" class="form-control" placeholder="Ingrese la serie" id="serie_insta"
                            name="serie_insta" value="" required readonly>
                    </div>

                    <div class="mb-3">
                        <label for="formOTinsta" class="form-label">Orden de trabajo</label>
                        <input type="text" class="form-control" placeholder="Ingrese la orden" id="formOT_insta"
                            name="formOT_insta" value="" required>
                    </div>

                    <div class="mb-3">
                        <label for="formOTinsta" class="form-label">RUT cliente</label>
                        <input type="text" class="form-control" placeholder="Ingrese el rut con guion y DV" id="rut_insta"
                            name="rut_insta" value="">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="exampleTextarea1">Observaciones</label>
                        <textarea class="form-control" id="obs_insta"
                            placeholder="De ser necesario complemente su ingreso aqui" name="obs_insta" cols="3" rows="5"
                            required></textarea>
                    </div>

                    <!-- Puedes agregar más campos según los campos de tu tabla TB_NOTIFICACIONES -->

                    <div class="form-group">
                        <label class="form-label" for="customFile3">Cargar Archivo</label>
                        <input class="form-control border-0" name="fileInsta" id="fileInsta" type="file">
                    </div>

                    <button type="button" class="btn btn-success" id="instalButton" >Declarar instalada</button>
                </form>

            </div>
        </div>

        <!-- Offcanvas TRANSFERENCIA REVERSA   -->

        <div class="offcanvas offcanvas-end" id="offcanvasrevSuper" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">

            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <!-- REVERSA /  Contenido del  formulario   -->
            <div class="offcanvas-body p-4">
                <h5>Validación supervisor en reversa</h5>

                <div class="mb-3">
                    <label for="serie_tran" class="form-label">SERIE SELECCIONADA</label>
                    <input type="text" class="form-control" id="serie_trans_rever" name="serie_trans_rever" value=""
                        readonly required>
                </div>

                <!-- Puedes agregar más campos según los campos de tu tabla TB_NOTIFICACIONES -->

                <div class="mb-3">
                    <label for="icon_bootstrap" class="form-label">RUT</label>
                    <input type="text" class="form-control" id="rutReversa" name="rutReversa">
                </div>

                <div class="mb-3">
                    <label for="icon_bootstrap" class="form-label">ORDEN DE TRABAJO</label>
                    <input type="text" class="form-control" id="ordenReversa" name="ordenReversa" required>
                </div>

                <div class="mb-3">
                    <label for="icon_bootstrap" class="form-label">Transferencia supervisor</label>
                    <input type="text" class="form-control" id="super_destino" name="super_destino"
                        value=<?php echo $id_supervisor; ?> list="list_tecnico" readonly required>
                </div>

                <div class="form-group mb-0">
                    <label class="form-label" id="label_motivo" for="defaultSelectSm">Seleccionar motivo</label>
                    <select class="form-select form-select-sm form-control-clicked" id="listReversa" name="defaultSelectSm"
                        aria-label="Default select example">
                        <option value="" selected>SELECCIONAR</option>
                        <option value="perdidaTecnico">PERDIDA DE CLIENTE</option>
                        <option value="robo">ROBO</option>
                        <option value="Serie Incorrecta">SERIE EQUIPO INCORRECTA</option>
                        <option value="serieMalDesprovisionada">SERIE MAL DESPROVISIONADA</option>
                        <option value="serieInstalada">Equipo instalado en reversa</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="icon_bootstrap" class="form-label">SERIE FISICA RETIRADA</label>
                    <input type="text" class="form-control" placeholder="" id="serieNewReversa" name="serieNewReversa"
                        required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="exampleTextarea1">Observaciones</label>
                    <textarea class="form-control" id="obs_rev_tra" name="obs_rev_tra" cols="3" rows="5"
                        required></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label" for="customFile3">Cargar Archivo</label>
                    <input class="form-control border-0" name="userfile" id="userfile" type="file">
                </div>

                <button type="button" class="btn btn-warning" id="transferReversaButton">Solicitar requerimiento</button>

            </div>
        </div>

        <!-- Offcanvas ENTREGA DE REVERSA   -->

        <div class="offcanvas offcanvas-end" id="offcanvasReversaDeclara" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">

            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <!-- REVERSA /  Contenido del  formulario   -->
            <div class="offcanvas-body p-4">
                <h5>Declaración de entrega para reversa</h5>

                <div class="mb-3">
                    <label for="serie_trans_rever" class="form-label">SERIE SELECCIONADA</label>
                    <input type="text" class="form-control" id="serieReversaDeclara" name="serie_trans_rever" value=""
                        readonly required>
                </div>

                <!-- Puedes agregar más campos según los campos de tu tabla TB_NOTIFICACIONES -->

                <div class="form-group">
                    <label class="form-label" for="customFile3">Cargar Archivo</label>
                    <input class="form-control border-0" name="fileReversaDecla" id="fileReversaDecla" type="file">
                </div>

                <button type="button" class="btn btn-warning" id="reversaDeclaraButton">Declarar entregada</button>

            </div>
        </div>

        <!-- Offcanvas HISTORIAL  -->

        <div class="offcanvas offcanvas-end" id="offcanvasHistorial" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">

            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <!-- Offcanvas Body with Form -->
            <div class="offcanvas-body p-4">
                <h5>Historial de movimiento</h5>

                <div class="mb-3">
                    <label for="icon_bootstrap" class="form-label">SERIE</label>
                    <input type="text" class="form-control" placeholder="" id="serieHistorial" name="serieHistorial"
                        readonly>
                </div>

                <div class="mb-3">
                    <label for="icon_bootstrap" class="form-label">PRECIO</label>
                    <input type="text" class="form-control" placeholder="" id="precioHistorial" name="precioHistorial"
                        readonly>
                </div>

                <div class="timeline-container" id="webHistorial">

                </div>

            </div>
        </div>

        <!-- Elemento visual por pixelación de pagina y celulares -->
        <div class="container" style="height:100px">
        </div>

        <!-- Offcanvas para notificaciones de campana -->
        <div class="offcanvas offcanvas-end" id="affanOffcanvas2" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">
            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <div class="offcanvas-body p-0">
                <div class="sidenav-profile bg-gradient">
                    <div class="sidenav-style1"></div>
                    <!-- Información del usuario -->
                    <div class="user-info">
                        <h6 class="user-name mb-0">Actividades más recientes</h6>
                        <span>TELQWAY</span>
                    </div>
                    <!-- Botón para crear tarea (opcional) -->
                    <!-- <a class="btn m-1 btn-info" href="#">
            <i class="bi bi-cursor"></i> Crear Tarea
            </a> -->
                </div>
                <!-- Contenido de las actividades -->
                <div class="sidenav-wrapper2">
                    <!-- Las actividades serán agregadas dinámicamente aquí -->
                </div>
            </div>
        </div>

        <div class="add-new-contact-wrap">
            <a class="shadow" href="#" data-bs-toggle="offcanvas" style="right:123px;" data-bs-target="#soporteTecnico"
                aria-controls="affanOffcanvas">
                <i class="bi bi-headset"></i>
            </a>
        </div>
<!-- 
        <div class="add-new-contact-wrap">
            <a class="shadow" href="#" onclick="validateBeforeOpen(event, '<?php echo $usuario; ?>')" style="right:71px;">
                <i class="bi bi-cart-plus"></i>
            </a>
        </div> -->

        <div class="add-new-contact-wrap">
            <a class="shadow" href="#" data-bs-toggle="offcanvas" data-bs-target="#MenuSerieReversa"
                aria-controls="menuSerieReversaOffcanvas">
                <i class="bi bi-plus"></i>
            </a>
        </div>

        <!----------------------------------------------------------------------------------------------------
    ------------------------------------------- AUXILIARES PARA FORMULARIOS LATERALES --------------------------------------
    --------------------------------------------------------------------------------------

    ----Offcanvas PARA INVENTARIO -->
        <div class="navbar--toggler" id="affanNavbarInventario" data-bs-toggle="offcanvas"
            data-bs-target="#offcanvasInventario" aria-controls="affanOffcanvas" style="border: thin;">
            Mostrar Offcanvas
        </div>

        <!----Offcanvas PARA TRANSFERIR -->
        <div class="navbar--toggler" id="affanNavbarToggler2" data-bs-toggle="offcanvas" data-bs-target="#offcanvasrigh"
            aria-controls="affanOffcanvas" style="border: thin;">
            Mostrar Offcanvas
        </div>

        <!-- Offcanvas PARA EL FORMULARIO DE INSTALACIÓN DE TÉCNICO -->
        <div class="navbar--toggler" id="div_instala" data-bs-toggle="offcanvas" data-bs-target="#offcanvasInstala"
            aria-controls="affanOffcanvas" style="border: thin;">
            Mostrar Offcanvas
        </div>

        <!-- Offcanvas PARA REVERSA TRANSFERENCIA A SUPER -->
        <div class="navbar--toggler" id="div_aux_canva_reversa" data-bs-toggle="offcanvas"
            data-bs-target="#offcanvasrevSuper" aria-controls="affanOffcanvas" style="border: thin;">
            Mostrar Offcanvas
        </div>

        <!-- Offcanvas PARA HISTORIAL -->
        <div class="navbar--toggler" id="canvaHistorial" data-bs-toggle="offcanvas" data-bs-target="#offcanvasHistorial"
            aria-controls="affanOffcanvas" style="border: thin;">
            Mostrar Offcanvas
        </div>

        <!-- Offcanvas PARA HISTORIAL -->
        <div class="navbar--toggler" id="canvaReversaDeclara" data-bs-toggle="offcanvas"
            data-bs-target="#offcanvasReversaDeclara" aria-controls="affanOffcanvas" style="border: thin;">
            Mostrar Offcanvas
        </div>
        <!----------------------------------------------------------------------------------------------------
    -------------------------------------------FOOTER Y JS --------------------------------------
    --------------------------------------------------------------------------------------




        FOOTER-->

        <div id="loadingOverlay" style="display: none;">
            <div id="loadingSpinner">
                <i class="fas fa-clock"></i> <!-- Icono de reloj de FontAwesome -->
            </div>
        </div>

        <?php echo include('footer_tecnico_logist.php') ?>

        <!--  SCRIPT CARRITO DE COMPRAS    -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    </body>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/slideToggle.min.js"></script>
    <script src="js/internet-status.js"></script>
    <script src="js/tiny-slider.js"></script>
    <script src="js/venobox.min.js"></script>
    <script src="js/countdown.js"></script>
    <script src="js/rangeslider.min.js"></script>
    <script src="js/vanilla-dataTables.min.js"></script>
    <script src="js/index.js"></script>
    <script src="js/imagesloaded.pkgd.min.js"></script>
    <script src="js/isotope.pkgd.min.js"></script>
    <!-- <script src="js/dark-rtl.js"></script> -->
    <script src="js/active.js"></script>
    <script src="js/pwa.js"></script>
    <script src="js/chart-active.js"></script>
    <script src="js/apexcharts.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Librería Bootstrap (requiere jQuery) -->
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        // Función para limpiar el campo al cargar la página
        window.addEventListener('load', function() {
            var campo = document.getElementById('motivo_tran');
            campo.value = ''; // Limpiar el contenido del campo
        });
    </script>

    <script>
        // Función para cargar el historial usando fetch API
        function cargarHistorial(serie) {
            // Limpiar el contenido del elemento web antes de enviar la solicitud
            document.getElementById('webHistorial').innerHTML = '';
            fetch(`GET_LOGISTICA.php?proceso=historial2&serie=${serie}`, {
                    method: 'GET', // o POST si prefieres enviar datos en el body
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(response.statusText);
                    }
                    return response.json();
                })
                .then(data => {
                    // Procesa la data y renderiza los elementos.
                    let html = '';
                    data.forEach(item => {
                        html += `<div class="card-item">
                            <div class="card-content">
                                <span class="card-date">Fecha: ${item.fecha_hora}</span>
                                <p class="card-info"><strong>Movimiento:</strong> ${item.Semantica}</p>
                                <p class="card-info"><strong>Desde:</strong> ${item.Nombre_origen}</p>`;
                        if (item.movimiento_historial) {
                            html += `<p class="card-info"><strong>Hacia:</strong> ${item.Nombre_destino}</p>`;
                        } else {
                            html += `<p class="card-info"><strong>Hacia:</strong> ${item.Nombre_destino}</p>`;
                        }
                        if (item.motivo) {
                            html += `<p class="card-info"><strong>Motivo:</strong> ${item.motivo}</p>`;
                        }
                        if (item.observacion) {
                            let textoCorto = item.observacion.substring(0, 30);
                            let textoMostrar = (item.observacion.length > 30) ? textoCorto + '...' : textoCorto;
                            html +=
                                `<p class="card-info" title="${item.observacion}"><strong>Observacion:</strong> ${textoMostrar}</p>`;
                        }
                        if (item.archivo_adj) {
                            html +=
                                `<p class="card-info"><a href="${item.archivo_adj}" download>Descargar respaldo</a></p>`;
                        }
                        html += `   </div>
                        </div>`;
                    });
                    document.getElementById('webHistorial').innerHTML = html;
                })
                .catch(error => {
                    console.error("Error al recibir la respuesta: ", error);
                });
        }
    </script>

    <script>
       function redirigirEnTransferencia(serial, item, ID_MOVI, accion) {
    console.log("Parámetros recibidos:");
    console.log("Serial: ", serial);
    console.log("Item: ", item);
    console.log("ID_MOVI: ", ID_MOVI);
    console.log("Accion: ", accion);

    // Verificar condiciones primero
    if (accion === 'TRANSFIERE') {
        if (ID_MOVI === '5') {
            alert('Serial ya fue declarado como instalado');
            return;
        } else if (ID_MOVI === '3') {
            alert('Aun pendiente por el usuario a quien escalaste el material');
            return;
        } else if (ID_MOVI === '4') {
            alert('El material ya fue justificado por el supervisor');
            return;
        } else if (ID_MOVI === '13') {
            alert('El material ha sido escalado a VTR ');
            return;
        }

        // Asignar el valor del serial antes de mostrar el offcanvas
        document.getElementById('serie_tran').value = serial;

        // Usar el método original para mostrar el offcanvas
        document.getElementById('affanNavbarToggler2').click();
    }
    else if (accion === 'VER') {
        // Ocultar el elemento <div> cuando se selecciona "INSTALA"
        document.getElementById('serieHistorial').value = serial;
        document.getElementById('precioHistorial').value = '$' + Math.floor(item);

        // Cargar historial primero
        cargarHistorial(serial);

        // Luego mostrar el offcanvas
        document.getElementById('canvaHistorial').click();
    }
    else if (accion === 'TRANSFIERE_REV') {
        if (ID_MOVI === '6') {
            alert('Serial ya fue declarado como instalado');
            return;
        } else if (ID_MOVI === '7') {
            alert('Serial ya fue declarado como instalado');
            return;
        }

        // Asignar el valor y mostrar el offcanvas
        document.getElementById('serie_trans_rever').value = serial;
        document.getElementById('div_aux_canva_reversa').click();
    }
    else if (accion === 'ENTREGA_REV') {
        document.getElementById('serieReversaDeclara').value = serial;
        document.getElementById('canvaReversaDeclara').click();
    }
    else if (accion === 'INSTALA') {
        if (ID_MOVI === '5') {
            alert('Serial ya fue declarado como instalado');
            return;
        } else if (ID_MOVI === '3') {
            alert('Aun pendiente por el usuario a quien escalaste el material');
            return;
        } else if (ID_MOVI === '4') {
            alert('El material ya fue justificado por el supervisor');
            return;
        } else if (ID_MOVI === '13') {
            alert('El material ha sido escalado a VTR ');
            return;
        }

        // Asignar valor y mostrar offcanvas
        document.getElementById('serie_insta').value = serial;
        document.getElementById('div_instala').click();
    }
}


function cerrarTodosOffcanvas() {
    // Buscar todos los offcanvas activos
    const activeOffcanvas = document.querySelectorAll('.offcanvas.show');

    // Cerrar cada uno simulando un clic en su botón de cerrar
    activeOffcanvas.forEach(function(canvas) {
        const closeButton = canvas.querySelector('.btn-close');
        if (closeButton) {
            closeButton.click();
        }
    });

    // Eliminar posibles backdrops que queden
    setTimeout(function() {
        const backdrops = document.querySelectorAll('.offcanvas-backdrop');
        backdrops.forEach(function(backdrop) {
            backdrop.classList.remove('show');
            setTimeout(function() {
                backdrop.remove();
            }, 300);
        });

        // Desenfocar cualquier botón que pueda haber quedado en foco
        document.activeElement.blur();
    }, 300);
}
    </script>

    <!-- BOTONES DE LA DIRECTA   -->
    <script>
        let tipoSolicitud = 0;
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM fully loaded and parsed');
            var carritoItems = [];
            // Función para verificar y registrar la existencia de un elemento
            function checkElement(id) {
                var element = document.getElementById(id);
                if (element) {
                    // console.log(`Element '${id}' exists in the DOM`);
                } else {
                    // console.log(`Element '${id}' is not in the DOM`);
                }
                return element;
            }
            // Verificar elementos comunes
            var agregarAlCarritoBtn = checkElement('agregar_al_carrito');
            var iconBootstrapInput = checkElement('icon_bootstrap');
            var cantidadInput = checkElement('cantidad');
            var carritoDiv = checkElement('carrito');
            // Verificar elementos especiales
            var inputSerie = checkElement('seriadoCarrito');
            var tecnicoSelect = checkElement('tecnicoTraspaso');
            // Verificar si los elementos comunes existen
            if (!agregarAlCarritoBtn || !iconBootstrapInput || !cantidadInput || !carritoDiv) {
                console.error('One or more required common elements are missing from the DOM');
                return;
            }
            agregarAlCarritoBtn.addEventListener('click', function() {
                var material = iconBootstrapInput.value;
                var cantidad = cantidadInput.value;
                // Validate that both material and cantidad are not null or empty
                if (material && material.trim() !== '' && cantidad && cantidad.trim() !== '') {
                    var item = {
                        material: material,
                        cantidad: cantidad
                    };
                    carritoItems.push(item);
                    actualizarCarrito();
                } else {
                    alert('Por favor, complete ambos campos antes de agregar al carrito.');
                }
                iconBootstrapInput.value = '';
                cantidadInput.value = '';
            });
            let userId = <?php echo json_encode($id_usuario); ?> ;
            // Funcionalidad especial para usuarios con ID 73 o 160
            if (inputSerie && tecnicoSelect) {
                inputSerie.addEventListener('input', () => {
                    console.log('Input serie changed');
                    if (tecnicoSelect.value.trim() !== '') {
                        setTimeout(() => {
                            var serie = inputSerie.value;
                            agregarAlCarrito2(serie, 1);
                        }, 100);
                    } else {
                        alert("Debes seleccionar un técnico.");
                    }
                });
            }

            function agregarAlCarrito2(material, cantidad) {
                console.log('agregarAlCarrito2 called');
                const item = {
                    material: material,
                    cantidad: cantidad
                };
                carritoItems.push(item);
                if (inputSerie) {
                    inputSerie.value = '';
                }
                actualizarCarrito();
            }

            function actualizarCarrito() {
                console.log('actualizarCarrito called');
                carritoDiv.innerHTML = '';
                if (carritoItems.length === 0) {
                    carritoDiv.innerHTML = `
                <div class="text-center text-muted p-3 bg-light rounded">
                    <i class="bi bi-cart3 fs-4"></i>
                    <p class="mb-0 mt-2">El carrito está vacío</p>
                </div>`;
                    return;
                }
                let cartHTML = '<div class="cart-container">';
                for (var i = 0; i < carritoItems.length; i++) {
                    var item = carritoItems[i];
                    var materialDisplay = item.material.length > 70 ? item.material.substring(0, 70) + '...' : item
                        .material;
                    cartHTML += `
                <div class="cart-item">
                    <div class="cart-item-content">
                        <div class="cart-item-details">
                            <span class="material-name">${materialDisplay}</span>
                            <div class="quantity-badge">
                                <i class="bi bi-box-seam"></i>
                                <span>${item.cantidad}</span>
                            </div>
                        </div>
                        <button class="delete-btn" onclick="window.eliminarDelCarrito(${i})"
                                title="Eliminar elemento">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>`;
                }
                cartHTML += '</div>';
                carritoDiv.innerHTML = cartHTML;
            }
            // Funciones globales
            window.eliminarDelCarrito = function(index) {
                console.log('eliminarDelCarrito called');
                carritoItems.splice(index, 1);
                actualizarCarrito();
            };
            window.confirmarSolicitud = function() {
                console.log('confirmarSolicitud called');
                let id_tecnico_traspaso = '';
                let serie_asignada = '';
                if (tecnicoSelect) {
                    id_tecnico_traspaso = tecnicoSelect.value;
                }
                if (inputSerie) {
                    serie_asignada = inputSerie.value;
                }
                var idUsuario = <?php echo json_encode($id_usuario); ?> ;
                console.log("Valor de idUsuario:", idUsuario);
                console.log("Valor de idUsuario2:", id_tecnico_traspaso);
                var confirmacion = confirm("¿Estás seguro de confirmar la solicitud?");
                if (confirmacion) {
                    guardarCarrito(idUsuario, id_tecnico_traspaso, serie_asignada);
                }
            };
            // At the top of your file, add this PHP variable to JavaScript
            const supervisorId = <?php echo $id_supervisor; ?> ;
            // Then update the function
            function guardarCarrito(idUsuario, id_tecnico2, serie_asignada) {
                console.log('guardarCarrito called');
                var xhr = new XMLHttpRequest();
                xhr.open("POST", "guardar_carrito.php", true);
                xhr.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === XMLHttpRequest.DONE) {
                        if (xhr.status === 200) {
                            alert('Registros guardados exitosamente.');
                            carritoItems = [];
                            actualizarCarrito();
                        } else {
                            alert('Error al guardar el carrito.');
                        }
                    }
                };
                var data = {
                    id_usuario: idUsuario,
                    id_destino: id_tecnico2,
                    carrito_items: carritoItems,
                    serie_asignada: serie_asignada,
                    id_supervisor: supervisorId // Use the global variable
                };
                console.log(data);
                xhr.send(JSON.stringify(data));
            }
            initSSE();
            // Referencia al primer botón
            const btnDirecta = document.getElementById("bootstrap-tab");
            // Referencia al segundo botón
            const btnRecepcion = document.getElementById("pwa-tab");
            // Referencia al segundo botón
            const btnTransfer = document.getElementById("dark-tab");
            // Referencia al segundo botón
            const btnReversa = document.getElementById("rever-tab");
            // Evento click para el primer botón
            btnDirecta.addEventListener("click", () => {
                tipoSolicitud = 1;
            });
            // Evento click para el segundo botón
            btnRecepcion.addEventListener("click", () => {
                tipoSolicitud = 2;
            });
            // Evento click para el segundo botón
            btnTransfer.addEventListener("click", () => {
                tipoSolicitud = 3;
            });
            // Evento click para el segundo botón
            btnReversa.addEventListener("click", () => {
                tipoSolicitud = 4;
            });
            // Imprime el valor cuando se hace clic
            btnDirecta.addEventListener('click', () => {
                // console.log(tipoSolicitud);
            });
            btnReversa.addEventListener('click', () => {
                // console.log(tipoSolicitud);
            });
            btnTransfer.addEventListener('click', () => {
                // console.log(tipoSolicitud);
            });
            btnRecepcion.addEventListener('click', () => {
                // console.log(tipoSolicitud);
            });
            let tecnicoLinkClickeado = false;
            const usuarioDestinoInput = document.getElementById('usuario_destino');
            // const listTecnico = document.getElementById('list_tecnico');
            const justificarLink = document.getElementById('justificarLink');
            const bodegaLink = document.getElementById('bodegaLink');
            const bodegaLinkTOA = document.getElementById('bodegaLinkTOA');
            const fotoCierreInv = document.getElementById('fotoCierreInv');
            const DevueltoBodega = document.getElementById('DevueltoBodega');
            const tecnicoLink = document.getElementById('tecnicoLink');
            const opcionesAsuper = document.getElementById('opcionesAsuper');
            const transferForm = document.getElementById('transferForm');
            const transferButton = document.getElementById('transferButton');
            const instalButton = document.getElementById('instalButton');
            const transferReversaButton = document.getElementById('transferReversaButton');
            const reversaDeclaraButton = document.getElementById('reversaDeclaraButton');
            // Common function to set up shared behavior
            function setupCommonBehavior(valorJustificar) {
                usuarioDestinoInput.value = valorJustificar;
                usuarioDestinoInput.disabled = true;
                tecnicoLinkClickeado = false;
                document.getElementById("divArchivo").style.display = "none";
                document.getElementById('tecnicoTransf').style.display = 'none';
                document.getElementById("val_tecnico_destino").value = valorJustificar;
                document.getElementById("val_tecnico_destino").style.display = 'none';
            }
            // Function to handle "Validación supervisor" button click
            function handleJustificarClick(event) {
                event.preventDefault();
                const valorJustificar = <?php echo $id_supervisor; ?> ;
                console.log(valorJustificar);
                setupCommonBehavior(valorJustificar);
                document.getElementById('motivo_tran_contain').style.display = 'none';
                document.getElementById('motivoASuper').style.display = 'block';
                document.getElementById('serie_tran_contain').style.display = 'none';
                document.getElementById("defaultSelectSm").style.display = 'block';
                document.getElementById("label_motivo").style.display = 'block';
            }
            // Function to handle "PROBLEMA SISTEMICO" button click
            function handleBodegaSistemicoClick(event) {
                event.preventDefault();
                const valorJustificar = <?php echo $id_supervisor; ?> ;
                setupCommonBehavior(valorJustificar);
                document.getElementById('motivo_tran_contain').style.display = 'block';
                document.getElementById('motivo_tran').value = 'PROBLEMA SISTEMICO';
                document.getElementById('motivoASuper').style.display = 'none';
                document.getElementById('serie_tran_contain').style.display = 'none';
            }
            // Function to handle "Equipo serie incorrecta" button click
            function handleBodegaSeriIncorrectaClick(event) {
                event.preventDefault();
                const valorJustificar = <?php echo $id_supervisor ?> ;
                setupCommonBehavior(valorJustificar);
                document.getElementById('motivo_tran_contain').style.display = 'block';
                document.getElementById('motivo_tran').value = 'PROBLEMA SISTEMICO';
                document.getElementById('serie_tran_contain').style.display = 'block';
                document.getElementById("defaultSelectSm").style.display = 'none';
                document.getElementById("label_motivo").style.display = 'none';
            }
            // Add event listeners
            document.getElementById('justificarLink').addEventListener('click', handleJustificarClick);
            document.getElementById('bodegaSistemico').addEventListener('click', handleBodegaSistemicoClick);
            document.getElementById('bodegaSeriIncorrecta').addEventListener('click',
                handleBodegaSeriIncorrectaClick);

            // Manejar el clic en el botón Para Bodega
            bodegaLink.addEventListener('click', function(event) {
                event.preventDefault();
                // usuarioDestinoInput.value = 164;
                // usuarioDestinoInput.disabled = true;
                // listTecnico.disabled = true;
                tecnicoLinkClickeado = false;
                const divArchivo = document.getElementById("divArchivo");
                divArchivo.style.display = "none";
                document.getElementById('motivo_tran_contain').style.display = 'block';
                // document.getElementById('label_motivo').style.display = 'none';
                // Ocultar el combo box con id "opciones"
                var hiddenInput = document.getElementById('usuario_destino_hidden');
                if (hiddenInput) {
                    hiddenInput.value = '164';
                }
                // document.getElementById('usuario_destino').value = 164;
                document.getElementById('motivo_tran').value = 'FALLO ELECTRONICO';
                document.getElementById('tecnicoTransf').style.display = 'none';
                document.getElementById('serie_tran_contain').style.display = 'none';
                document.getElementById('motivoASuper').style.display = 'none';
            });
            // Manejar el clic en el botón Para Bodega
            fotoCierreInv.addEventListener('click', function(event) {
                event.preventDefault();
                usuarioDestinoInput.value = '164';
                usuarioDestinoInput.disabled = true;
                // listTecnico.disabled = true;
                tecnicoLinkClickeado = false;
                document.getElementById('motivo_tran_contain').style.display = 'block';
                // document.getElementById('label_motivo').style.display = 'none';
                // Ocultar el combo box con id "opciones"
                const divArchivo = document.getElementById("divArchivo");
                divArchivo.style.display = "block";
                document.getElementById('motivo_tran').value = 'SERIE ENCONTRADA';
                document.getElementById('tecnicoTransf').style.display = 'none';
                document.getElementById('serie_tran_contain').style.display = 'none';
                document.getElementById('motivoASuper').style.display = 'none';
            });
            // Manejar el clic en el botón Para Bodega
            DevueltoBodega.addEventListener('click', function(event) {
                event.preventDefault();
                // usuarioDestinoInput.value = 164;
                // usuarioDestinoInput.disabled = true;
                // listTecnico.disabled = true;
                tecnicoLinkClickeado = false;
                const divArchivo = document.getElementById("divArchivo");
                divArchivo.style.display = "none";
                document.getElementById('motivo_tran_contain').style.display = 'block';
                // document.getElementById('label_motivo').style.display = 'none';
                // Ocultar el combo box con id "opciones"
                var hiddenInput = document.getElementById('usuario_destino_hidden');
                if (hiddenInput) {
                    hiddenInput.value = '164';
                }
                // document.getElementById('usuario_destino').value = 164;
                document.getElementById('motivo_tran').value = 'DEVUELTO A BODEGA';
                document.getElementById('tecnicoTransf').style.display = 'none';
                document.getElementById('serie_tran_contain').style.display = 'none';
                document.getElementById('motivoASuper').style.display = 'none';
            });
            // Manejar el clic en el botón Para Bodega
            bodegaLinkTOA.addEventListener('click', function(event) {
                event.preventDefault();
                usuarioDestinoInput.value = '164';
                usuarioDestinoInput.disabled = true;
                // listTecnico.disabled = true;
                tecnicoLinkClickeado = false;
                document.getElementById('motivo_tran_contain').style.display = 'block';
                // document.getElementById('label_motivo').style.display = 'none';
                // Ocultar el combo box con id "opciones"
                const divArchivo = document.getElementById("divArchivo");
                divArchivo.style.display = "none";
                document.getElementById('motivo_tran').value = 'SERIE NO APARECE EN TOA';
                document.getElementById('tecnicoTransf').style.display = 'none';
                document.getElementById('serie_tran_contain').style.display = 'none';
                document.getElementById('motivoASuper').style.display = 'none';
            });

            // Manejar el clic en el botón Para Técnico
            tecnicoLink.addEventListener('click', function(event) {
                event.preventDefault();
                usuarioDestinoInput.value = '';
                usuarioDestinoInput.disabled = false;
                // listTecnico.disabled = false;
                tecnicoLinkClickeado = true; // VARIABLE PARA SELECCION DE TECNICO
                console.log(tecnicoLinkClickeado);
                document.getElementById('serie_tran_contain').style.display = 'none';
                document.getElementById('motivoASuper').style.display = 'none';
                const divArchivo = document.getElementById("divArchivo");
                divArchivo.style.display = "none";
                document.getElementById('tecnicoTransf').style.display = 'block';
                document.getElementById('motivo_tran_contain').style.display = 'block';
                // document.getElementById('label_motivo').style.display = 'block';
                // Ocultar el combo box con id "opciones"
                document.getElementById('motivo_tran').value = 'Por transferencia a otro tecnico';
            });
            document.getElementById('bodegaSeriIncorrecta').addEventListener('click', function() {
                document.getElementById('motivo_tran').value = "La serie del equipo es incorrecta.";
            });
            // Add event listeners for the buttons
            document.getElementById('bodegaSistemico').addEventListener('click', function() {
                document.getElementById('motivo_tran').value = "Problema sistémico detectado.";
            });
            reversaDeclaraButton.addEventListener('click', function() {
                const serie = document.getElementById('serieReversaDeclara').value;
                const ticket = ''; // Agrega el valor del ticket aquí
                const tecnico_origen = <?php echo $id_usuario ?> ;
                const tecnico_destino = <?php echo $id_usuario ?> ;
                const motivo = 'DECLARADA INSTALADA';
                // Llamada a la función actualizarRegisto con los parámetros
                transferirRegistro(serie, ticket, tecnico_origen, tecnico_destino, motivo,
                    'ReversaEntregada');
                // Ocultar el elemento offcanvas
                ocultarOffcanvas();
            });
            transferButton.addEventListener('click', function() {
                try {
                    console.log('Iniciando proceso de transferencia');
                    const hiddenInputValue = document.getElementById('usuario_destino_hidden').value;
                    console.log('Usuario destino (hidden):', hiddenInputValue);

                    if (confirm("¿Estás seguro de que deseas transferir el registro?")) {
                        if (tecnicoLinkClickeado && (!usuarioDestinoInput.value || usuarioDestinoInput.value === '0')) {
                            console.log('Error: Usuario destino inválido');
                            alert('Debes ingresar un usuario valido');
                            return;
                        }

                        const serie_ING = document.getElementById('serie_tran_new').value;
                        const serie = document.getElementById('serie_tran').value;
                        let ticket = serie_ING || '';
                        const recurso_nuevo = document.getElementById("val_tecnico_destino").value;
                        const tecnico_origen = <?php echo $id_usuario ?>;
                        const motivo = document.getElementById('motivo_tran').value;
                        let tecnico_destino;

                        console.log({
                            serie_ING,
                            serie,
                            ticket,
                            recurso_nuevo,
                            tecnico_origen,
                            motivo
                        });

                        // Determinar técnico destino
                        if (motivo === "SERIE NO APARECE EN TOA" || motivo === "DEVUELTO A BODEGA") {
                            tecnico_destino = "164";
                        } else if (motivo === "La serie del equipo es incorrecta." ||
                                motivo === "Problema sistémico detectado." ||
                                motivo === "perdidaTecnico") {
                            tecnico_destino = document.getElementById("val_tecnico_destino").value;
                        } else {
                            tecnico_destino = usuarioDestinoInput.value;
                        }

                        console.log('Técnico destino final:', tecnico_destino);

                        // Realizar la transferencia
                        transferirRegistro(serie, ticket, tecnico_origen, tecnico_destino, motivo, 'transfiere');
                        ocultarOffcanvas();
                    }
                } catch (error) {
                    console.error('Error en el proceso de transferencia:', error);
                    alert('Ocurrió un error durante la transferencia');
                }
            });
            instalButton.addEventListener('click', function() {
                const serie = document.getElementById('serie_insta').value;
                const rut = document.getElementById('rut_insta').value;
                const ot = document.getElementById('formOT_insta').value;
                const ticket = ''; // Agrega el valor del ticket aquí
                const tecnico_origen = <?php echo $id_usuario ?> ;
                const tecnico_destino = <?php echo $id_usuario ?> ;
                const motivo = document.getElementById('obs_insta').value;
                // Imprimir los valores en consola
                console.log('Serie:', serie);
                console.log('Rut:', rut);
                console.log('OT:', ot);
                console.log('Ticket:', ticket);
                console.log('Técnico origen:', tecnico_origen);
                console.log('Técnico destino:', tecnico_destino);
                console.log('Motivo:', motivo);
                // Verificar si ambos campos tienen valores ingresados
                if (ot.trim() !== '') {
                    // Llamada a la función actualizarRegitro con los parámetros
                    transferirRegistro(serie, ticket, tecnico_origen, tecnico_destino, motivo, 'instala');
                    document.getElementById('formOT_insta').value = '';
                    document.getElementById('rut_insta').value = '';
                    document.getElementById('obs_insta').value = '';
                    document.getElementById('fileInsta').value = '';
                    // Ocultar el elemento offcanvas
                    ocultarOffcanvas();
                } else {
                    // Mostrar un mensaje de error o realizar otra acción si los campos no tienen valores ingresados
                    alert('Por favor, ingrese Rut y Orden de Trabajo');
                }
            });
            transferReversaButton.addEventListener('click', function() {
                // Obtener los valores de los campos adicionales
                const rutReversa = document.getElementById('rutReversa').value;
                const ordenReversa = document.getElementById('ordenReversa').value;
                // Validación de campos
                if (!ordenReversa) {
                    alert('Por favor, ingrese la orden para completar la solicitud.');
                    return; // Detener la ejecución si no se cumplen las validaciones
                }
                const serie = document.getElementById('serie_trans_rever').value;
                const ticket = document.getElementById('serieNewReversa')
                    .value; // Agrega el valor del ticket aquí
                const tecnico_origen = <?php echo $id_usuario ?> ;
                const tecnico_destino = document.getElementById('super_destino').value;
                const motivo = document.getElementById('listReversa').value;
                // console.log("Elemento rutReversa:", document.getElementById('rutReversa')).value;
                // Llamada a la función actualizarRgistro con los parámetros
                transferirRegistro(serie, ticket, tecnico_origen, tecnico_destino, motivo, 'transfiereRev');
                // Ocultar el elemento offcanvas
                ocultarOffcanvas();
                document.getElementById('rutReversa').value = '';
                document.getElementById('ordenReversa').value = '';
                document.getElementById('serie_trans_rever').value = '';
                document.getElementById('listReversa').value = '';
                document.getElementById('obs_rev_tra').value = '';
                document.getElementById('serieNewReversa').value = '';
            });

            function transferirRegistro(Serial, ticket, id_tecnico, tecnico_destino, motivo, accion) {
                var formData = new FormData();
                const hiddenInputValue = document.getElementById('usuario_destino_hidden').value;
                // Condición para manejar el archivo si la acción es 'instala'
                if (accion === 'instala') {
                    const fileInput = document.getElementById('fileInsta');
                    formData.append('fileInsta', fileInput.files[0]);
                    const rut_instalado = document.getElementById('rut_insta').value;
                    const OT_instala = document.getElementById('formOT_insta').value;
                    formData.append('rut_instalado', rut_instalado);
                    formData.append('OT_instala', OT_instala);
                }
                if (accion === 'transfiere') {
                    const fileInput = document.getElementById('fileInventario');
                    formData.append('fileInventario', fileInput.files[0]);
                }
                // Condición para manejar el archivo si la acción
                if (accion === 'transfiereRev') {
                    const fileInput = document.getElementById('userfile');
                    // Verificar si hay un archivo adjunto
                    if (fileInput.files.length > 0) {
                        formData.append('userfile', fileInput.files[0]);
                    } else {
                        // Si no hay archivo, añadir una cadena vacía
                        formData.append('userfile', '');
                    }
                    const rutReversa = document.getElementById('rutReversa').value;
                    const ordenReversa = document.getElementById('ordenReversa').value;
                    const ObservaReversa = document.getElementById('obs_rev_tra').value;
                    formData.append('rut_instalado', rutReversa);
                    formData.append('OT_instala', ordenReversa);
                    formData.append('ObservaReversa', ObservaReversa);
                }
                if (accion === 'ReversaEntregada') {
                    const fileInput = document.getElementById('fileReversaDecla');
                    // Verificar si hay un archivo adjunto
                    if (fileInput.files.length > 0) {
                        formData.append('fileReversaDecla', fileInput.files[0]);
                    } else {
                        // Si no hay archivo, añadir una cadena vacía
                        formData.append('fileReversaDecla', '');
                    }
                }
                formData.append('Serial', Serial);
                formData.append('accion', accion);
                formData.append('id_tecnico_origen', id_tecnico);
                formData.append('id_tecnico_destino', tecnico_destino);
                if (hiddenInputValue) {
                    formData.append('id_tecnico_destino', hiddenInputValue);
                } else {
                    formData.append('id_tecnico_destino', tecnico_destino);
                }
                formData.append('ticket', ticket);
                formData.append('motivo', motivo);
                // Mostrar todos los pares clave/valor
                formData.forEach(function(value, key) {
                    console.log(key + ': ' + value);
                });
                // Realiza la solicitud POST utilizando AJAX
                var request = new XMLHttpRequest();
                request.open('POST', 'GET_LOGIS_DIRECTA.php');
                request.onload = function() {
                    // Procesa la respuesta del servidor si es necesario
                    if (request.status === 200) {
                        // Maneja la respuesta del servidor
                        console.log(request.responseText);
                        alert("Datos enviados exitosamente.");
                    }
                };
                request.send(formData);
            }

                        function ocultarOffcanvas() {
            try {
                // Lista de IDs de offcanvas que queremos cerrar
                const offcanvasElements = [
                'offcanvasInventario',
                'offcanvasrigh',
                'offcanvasInstala',
                'offcanvasrevSuper',
                'offcanvasReversaDeclara',
                'offcanvasHistorial'
                ];

                // Cerrar cada offcanvas usando el botón de cierre, que respeta el flujo de Bootstrap
                offcanvasElements.forEach(id => {
                const element = document.getElementById(id);
                if (element && element.classList.contains('show')) {
                // Buscar y usar el botón de cierre
                const closeButton = element.querySelector('.btn-close');
                if (closeButton) {
                closeButton.click();
                } else {
                // Fallback: hacerlo manualmente si no hay botón
                element.classList.remove('show');

                // Restaurar propiedades importantes para que pueda mostrarse nuevamente
                element.style.display = ''; // Importante: no establecer a 'none'

                if (element.getAttribute('aria-modal') === 'true') {
                element.setAttribute('aria-modal', 'false');
                }
                }
                }
                });

                // Asegurarse de que los backdrops se eliminen correctamente
                setTimeout(() => {
                const backdrops = document.querySelectorAll('.offcanvas-backdrop');
                backdrops.forEach(backdrop => {
                backdrop.remove();
                });

                // Restaurar el scroll
                document.body.classList.remove('overflow-hidden');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
                }, 350); // Un poco más de tiempo para que termine la animación

                } catch (error) {
                console.error('Error al ocultar offcanvas:', error);
                }
            }
        });
    </script>

    <!-- SCRIPT PARA UNA  TRANSFERENCIA DIRECTA , A BODEGA , CON MOTIVO INCLUIDO-->
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            var selectElement = document.getElementById("defaultSelectSm");
            selectElement.addEventListener("change", function() {
                var selectedValue = this.value;
                document.getElementById("motivo_tran").value = selectedValue;
            });
        });
        /// ESTA ES LA SECCION DE BOTONOS PARA LA DIRECTA DEL TECNICO
    </script>

    <!-- SCRIPT / POST / MYSQL INSERT MOVIMIENTOS  -->
    <script>
        function actualizarRegistro(Serial, ticket, id_tecnico, accion) {
            // Crea un objeto FormData para enviar los datos en el POST
            const buttonElement = event.target;
            var formData = new FormData();
            if (accion === 'ENTREGA_REV') {
                if (id_tecnico === '6') {
                    alert('Bodega tiene pendiente la confirmación');
                } else if (id_tecnico === '7') {
                    alert('Material pendiente de revisión por supervisor');
                } else {
                    // Otra lógica si la acción no es 'rechaza'
                    formData.append('Serial', Serial);
                    formData.append('accion', accion);
                    formData.append('id_tecnico_origen', <?php echo $id_usuario; ?> );
                    formData.append('ticket', ticket);
                    // Realiza la solicitud POST utilizando AJAX
                    var request = new XMLHttpRequest();
                    request.open('POST', 'GET_LOGIS_DIRECTA.php');
                    request.onload = function() {
                        // Procesa la respuesta del servidor si es necesario
                        if (request.status === 200) {
                            // Maneja la respuesta del servidor
                            console.log(request.responseText);
                            const row = buttonElement.closest('tr');
                            row.parentNode.removeChild(row);
                        }
                    };
                    request.send(formData);
                }
            } else {
                if (accion === 'ACEPTA') {
                    //// ID_MOVIMIENTO
                    if (id_tecnico == 2) {
                        alert("NO PODRÁS CONFIRMAR HASTA LA REVISIÓN DE BODEGA");
                        console.log(id_tecnico);
                    } else {
                        // Otra lógica si la acción no es 'rechaza'
                        formData.append('Serial', Serial);
                        formData.append('accion', accion);
                        formData.append('id_tecnico_origen', <?php echo $id_usuario; ?> );
                        formData.append('ticket', ticket);
                        // Realiza la solicitud POST utilizando AJAX
                        var request = new XMLHttpRequest();
                        request.open('POST', 'GET_LOGIS_DIRECTA.php');
                        request.onload = function() {
                            // Procesa la respuesta del servidor si es necesario
                            if (request.status === 200) {
                                // Maneja la respuesta del servidor
                                console.log(request.responseText);
                            }
                        };
                        request.send(formData);
                        var row = buttonElement.closest('tr');
                        row.parentNode.removeChild(row);
                        this.disabled = true;
                    }
                }
            }
        }
    </script>

    <script>
        function actualizarRegistro_stock(Serial, ticket, id_tecnico, accion) {
            // Crea un objeto FormData para enviar los datos en el POST
            var formData = new FormData();
            formData.append('Serial', Serial);
            formData.append('accion', accion);
            formData.append('id_tecnico_origen', id_tecnico);
            formData.append('ticket', ticket);
            // Realiza la solicitud POST utilizando AJAX
            var request = new XMLHttpRequest();
            request.open('POST', 'GET_LOGIS_DIRECTA.php');
            request.onload = function() {
                // Procesa la respuesta del servidor si es necesario
                if (request.status === 200) {
                    // Maneja la respuesta del servidor
                    console.log(request.responseText);
                }
            };
            request.send(formData);
            GET_TABLA_MOVIMI_stock();
        }
    </script>

    <script>
        function Rechazoaceptar() {
            var motivoSeleccionado = document.getElementById('motivoRechazo').value;
            var Serial = document.getElementById('popupSerial').value;
            var ticket = document.getElementById('popupTicket').value;
            var id_tecnico_destino = document.getElementById('popupIdTecnicoDestino').value;
            var accion = document.getElementById('popupAccion').value;
            document.getElementById('popup-container').style.display = 'none';
            var formData = new FormData();
            formData.append('Serial', Serial);
            formData.append('accion', accion);
            formData.append('id_tecnico_origen', <?php echo $id_usuario ?> );
            formData.append('id_tecnico_destino', id_tecnico_destino);
            formData.append('ticket', ticket);
            formData.append('motivo', motivoSeleccionado);
            var request = new XMLHttpRequest();
            request.open('POST', 'GET_LOGIS_DIRECTA.php');
            request.onload = function() {
                if (request.status === 200) {
                    console.log(request.responseText);
                    // Find and remove the row with matching Serial
                    var table = document.getElementById('tablaAsignacion');
                    var rows = table.getElementsByTagName('tr');
                    for (var i = 1; i < rows.length; i++) { // Start from 1 to skip header row
                        var cells = rows[i].getElementsByTagName('td');
                        if (cells.length > 1 && cells[1].innerText === Serial) {
                            table.deleteRow(i);
                            break;
                        }
                    }
                }
            };
            request.send(formData);
        }

        function Rechazocancelar() {
            // Ocultar el cuadro de diálogo sin hacer nada
            document.getElementById('popup-container').style.display = 'none';
        }

        function rechazoMaterial(Serial, ticket, id_tecnico_destino, accion) {
            // Almacenar los parámetros en elementos ocultos
            document.getElementById('popupSerial').value = Serial;
            document.getElementById('popupTicket').value = ticket;
            document.getElementById('popupIdTecnicoDestino').value = ticket;
            document.getElementById('popupAccion').value = accion;
            // Mostrar el cuadro de diálogo
            document.getElementById('popup-container').style.display = 'block';
        }
    </script>

    <script>
        let eventSource;

        function initSSE() {
            var userId = <?php echo json_encode($id_usuario); ?> ;
            eventSource = new EventSource('sse_logistica.php?user_id=' + userId);
            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                // Punto de control 5: Recepción de mensaje
                // console.log('Received SSE message:', data);
                if (data.type === 'log') {
                    // console.log('SSE Log:', data.message);
                } else if (data.type === 'update') {
                    const tabla = document.getElementById(data.table);
                    if (tabla) {
                        // Verifica si la tabla es 'TablaDirecta'
                        if (data.table === 'TablaDirecta') {
                            // Si el id_movimiento es 1, agrega una nueva fila a la tabla
                            if (data.id_movimiento == 1) {
                                // console.log('Data id_movimiento:', data.id_movimiento);
                                tabla.innerHTML += data.html;
                            }
                            // Obtiene todas las filas de la tabla
                            const rows = tabla.getElementsByTagName('tr');
                            // Variable para controlar si se actualizó alguna fila
                            let rowUpdated = false;
                            // Recorre todas las filas de la tabla (empezando desde 1 para saltar el encabezado)
                            for (let i = 1; i < rows.length; i++) {
                                // Obtiene las celdas de la fila actual
                                const cells = rows[i].getElementsByTagName('td');
                                // Compara si el serial en la primera celda coincide con el serial recibido
                                if (cells[0].innerText === data.serial) {
                                    // Elimina la fila actual
                                    tabla.deleteRow(i);
                                    // Si el id_movimiento no es 15, agrega la nueva fila HTML
                                    if (data.id_movimiento != 15) {
                                        tabla.innerHTML += data.html;
                                    }
                                    // Marca que se actualizó una fila
                                    rowUpdated = true;
                                    // Sale del ciclo
                                    break;
                                }
                            }
                            // Si no se actualizó ninguna fila, agrega una nueva al final
                            if (!rowUpdated) {
                                tabla.innerHTML += data.html;
                            }

                        } else if (data.table === 'tablaAsignacion') {
                            const rows = tabla.getElementsByTagName('tr');
                            for (let i = 1; i < rows.length; i++) {
                                const cells = rows[i].getElementsByTagName('td');
                                if (cells[1].innerText === data.serial) {
                                    tabla.deleteRow(i);
                                    console.log('Row deleted from tablaAsignacion:', data.serial);
                                    break;
                                }
                            }
                            if (data.id_movimiento != 24) {
                                tabla.innerHTML += data.html;
                            }
                        } else if (data.table === 'poolBodegaReversa') {
                            const rows = tabla.getElementsByTagName('tr');
                            let rowUpdated = false;
                            for (let i = 1; i < rows.length; i++) {
                                const cells = rows[i].getElementsByTagName('td');
                                if (cells[0].innerText === data.serial) {
                                    tabla.deleteRow(i);
                                    tabla.innerHTML += data.html;
                                    rowUpdated = true;
                                    break;
                                }
                            }
                        }
                        // console.log('Table updated:', data.table);
                    } else {
                        // console.error('Table not found:', data.table);
                    }
                }
            };
            eventSource.onerror = function(error) {
                console.error('SSE Error:', error);
                eventSource.close();
                // Intentar reconectar después de 5 segundos
                setTimeout(initSSE, 5000);
            };
            // Punto de control 6: Conexión SSE iniciada
            console.log('SSE connection initialized');
        }
    </script>

    <script>
        $('#aceptarMasivo').click(function() {
            var idTecnico = <?php echo json_encode($id_usuario); ?> ;
            // Mostrar el spinner
            $('#spinner').show();
            $.ajax({
                url: 'POST_API.php?proceso=botonMasivo',
                type: 'POST',
                data: {
                    id_tecnico: idTecnico
                },
                beforeSend: function() {
                    // Otra opción es mostrar el spinner justo antes de enviar la solicitud
                    $('#spinner').show();
                    document.getElementById('dark-fondo').style.display =
                        'block'; // o 'flex' si es necesario
                },
                success: function(response) {
                    // Ocultar el spinner
                    $('#spinner').hide();
                    document.getElementById('dark-fondo').style.display = 'none';
                    console.log(response);
                },
                error: function() {
                    // Ocultar el spinner
                    $('#spinner').hide();
                    alert('Hubo un error al procesar tu solicitud');
                }
            });
        });
    </script>

    <script>
        // Función para filtrar tabla
        function filtrarTabla(input, tabla) {
            // Verificar que el input no sea null
            // Obtener valor de búsqueda
            let searchTerm = input.value.toLowerCase();
            // Obtener todas las filas de la tabla
            const rows = tabla.getElementsByTagName("tr");
            // Iterar sobre las filas de la tabla
            for (let i = 1; i < rows.length; i++) { // Comienza en 1 para omitir el encabezado de la tabla
                let cols = rows[i].getElementsByTagName("td");
                let match = false;
                // Buscar la palabra en cada celda
                for (let j = 0; j < cols.length; j++) {
                    if (cols[j].textContent.toLowerCase().includes(searchTerm)) {
                        match = true;
                        break; // Deja de buscar en otras celdas si ya hay una coincidencia
                    }
                }
                // Mostrar/Ocultar la fila basado en si hubo una coincidencia
                rows[i].style.display = match ? "" : "none";
            }
        }
        // Event listeners para cada input de búsqueda
        document.getElementById("searchInputtablaCierreInventario").addEventListener('keyup', function() {
            filtrarTabla(this, document.getElementById(this.getAttribute("data-table")));
        });
        document.getElementById("searchInputtablaAsignacion").addEventListener('keyup', function() {
            filtrarTabla(this, document.getElementById(this.getAttribute("data-table")));
        });
        document.getElementById("searchInputTablaDirecta").addEventListener('keyup', function() {
            filtrarTabla(this, document.getElementById(this.getAttribute("data-table")));
        });
        document.getElementById("searchInputpoolBodegaReversa").addEventListener('keyup', function() {
            filtrarTabla(this, document.getElementById(this.getAttribute("data-table")));
        });
    </script>

    <script>
        // Obtener elementos del DOM
        var modal = document.getElementById("modal_dev_bodega");
        // var openModalBtn = document.getElementById("openModalBtn");
        var closeBtn = document.getElementsByClassName("close")[0];
        var dataTable = document.getElementById("dataTable");
        // // Abrir el modal al hacer clic en el botón "Abrir Modal"
        // openModalBtn.onclick = function() {
        //     modal.style.display = "block";
        //     fetchData();
        // }
        // Cerrar el modal al hacer clic en la "x"
        closeBtn.onclick = function() {
            modal.style.display = "none";
        }
        // Cerrar el modal al hacer clic fuera del contenido del modal
        window.onclick = function(event) {
            if (event.target == modal) {
                modal.style.display = "none";
            }
        }
        // Función para realizar la solicitud GET y obtener los datos
        function fetchData() {
            var idUsuario = <?php echo $id_usuario; ?> ;
            var url = "GET_API.php?proceso=detalle_envio_bodega&ID_usuario=" + idUsuario;
            console.log(url);
            const xhr = new XMLHttpRequest();
            xhr.open("GET", url, true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === XMLHttpRequest.DONE) {
                    if (xhr.status === 200) {
                        const respuesta = xhr.responseText;
                        // Mostrar la respuesta directamente en la tabla tabla_detalle_inventario
                        const tablaDetalleInventario = document.getElementById('dataTable_bodega');
                        tablaDetalleInventario.innerHTML = respuesta;
                    } else {
                        console.error("Error:", xhr.status);
                    }
                }
            };
            xhr.send();
        }
    </script>



    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const familiaSelect = document.getElementById('familia_select');
            const materialSelect = document.getElementById('icon_bootstrap');
            // Cache para almacenar resultados
            const materialesCache = {};
            familiaSelect.addEventListener('change', function() {
                const familia = this.value;
                if (familia) {
                    materialSelect.disabled = false;
                    // Verificar si ya tenemos los datos en cache
                    if (materialesCache[familia]) {
                        materialSelect.innerHTML = materialesCache[familia];
                        return;
                    }
                    // Mostrar indicador de carga
                    materialSelect.innerHTML = '<option>Cargando...</option>';
                    fetch('get_materiales.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'familia=' + encodeURIComponent(familia)
                        })
                        .then(response => response.text())
                        .then(html => {
                            // Guardar en cache
                            materialesCache[familia] = html;
                            materialSelect.innerHTML = html;
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            materialSelect.innerHTML =
                                '<option value="">Error al cargar materiales</option>';
                        });
                } else {
                    materialSelect.disabled = true;
                    materialSelect.innerHTML = '<option value="">Seleccione un material</option>';
                }
            });
        });
    </script>





    <script>
        function validateBeforeOpen(event, id_usuario) {
            event.preventDefault();

            // Simular un clic en el botón que active el offcanvas directamente sin validación
            const offcanvasBtn = document.querySelector('[data-bs-target="#FormSolicitud"]');
            if (offcanvasBtn) {
                offcanvasBtn.click();
            } else {
                // Si no existe el botón, crear uno temporal y hacer clic
                const tempBtn = document.createElement('button');
                tempBtn.setAttribute('data-bs-toggle', 'offcanvas');
                tempBtn.setAttribute('data-bs-target', '#FormSolicitud');
                document.body.appendChild(tempBtn);
                tempBtn.click();
                document.body.removeChild(tempBtn);
            }
        }
    </script>

    <script>

        function makeTableSortable(tableId) {
            const table = document.getElementById(tableId);
            if (!table) return;

            const getCellValue = (tr, idx) => tr.children[idx].innerText || tr.children[idx].textContent;

            const comparer = (idx, asc) => (a, b) => {
                const v1 = getCellValue(asc ? a : b, idx);
                const v2 = getCellValue(asc ? b : a, idx);
                const num1 = parseFloat(v1);
                const num2 = parseFloat(v2);

                if (!isNaN(num1) && !isNaN(num2)) {
                    return num1 - num2;
                }
                return v1.toString().localeCompare(v2);
            };

            // Añadir estilos CSS para las flechas
            const style = document.createElement('style');
            style.textContent = `
                th.sortable {
                    cursor: pointer;
                    position: relative;
                    padding-right: 20px !important;
                }
                th.sortable::before,
                th.sortable::after {
                    content: '';
                    position: absolute;
                    right: 3px;
                    width: 0;
                    height: 0;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    opacity: 0.3;
                }
                th.sortable::before {
                    top: 30%;
                    border-bottom: 5px solid #000;
                }
                th.sortable::after {
                    bottom: 30%;
                    border-top: 5px solid #000;
                }
                th.sortable.asc::before {
                    opacity: 1;
                }
                th.sortable.desc::after {
                    opacity: 1;
                }
            `;
            document.head.appendChild(style);

            const headers = table.querySelectorAll('th');
            headers.forEach((th, colIndex) => {
                th.classList.add('sortable');

                th.addEventListener('click', () => {
                    const tbody = table.querySelector('tbody');
                    const rows = Array.from(tbody.querySelectorAll('tr'));

                    // Remover clases de ordenamiento previas
                    headers.forEach(header => {
                        header.classList.remove('asc', 'desc');
                    });

                    // Toggle dirección y añadir clase correspondiente
                    th.asc = !th.asc;
                    th.classList.add(th.asc ? 'asc' : 'desc');

                    // Ordenar filas
                    rows.sort(comparer(colIndex, th.asc))
                        .forEach(tr => tbody.appendChild(tr));
                });
            });
        }

        // Uso:
        document.addEventListener('DOMContentLoaded', function() {
            makeTableSortable('TablaDirecta');
            makeTableSortable('poolBodegaReversa');
            makeTableSortable('tablaAsignacion');
        });
    </script>

    <!-- Script para mantener la sesión activa y mejorar la navegación hacia atrás -->
    <script>
        // Función para mantener la sesión activa
        function keepSessionAlive() {
            // Crear un objeto XMLHttpRequest
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'session_ping.php?t=' + new Date().getTime(), true);
            xhr.send();
        }

        // Ejecutar la función cada 5 minutos (300000 ms)
        setInterval(keepSessionAlive, 300000);

        // Guardar el estado de la sesión en localStorage
        if (typeof localStorage !== 'undefined') {
            localStorage.setItem('sessionActive', 'true');
            localStorage.setItem('lastSessionCheck', new Date().toISOString());
            localStorage.setItem('currentPage', window.location.href);
        }

        // Mejorar la navegación hacia atrás
        window.addEventListener('pageshow', function(event) {
            // Si la página se carga desde la caché (navegación hacia atrás)
            if (event.persisted) {
                // Verificar si la sesión estaba activa
                if (localStorage.getItem('sessionActive') === 'true') {
                    // Hacer ping al servidor para mantener la sesión
                    keepSessionAlive();
                }
            }
        });

        // Capturar eventos de navegación
        window.addEventListener('beforeunload', function() {
            // Guardar la página actual en el historial de navegación
            if (typeof localStorage !== 'undefined') {
                let history = JSON.parse(localStorage.getItem('navigationHistory') || '[]');
                const currentPage = window.location.href;

                // Evitar duplicados
                if (history[history.length - 1] !== currentPage) {
                    history.push(currentPage);
                    // Mantener solo las últimas 10 páginas
                    if (history.length > 10) {
                        history = history.slice(-10);
                    }
                    localStorage.setItem('navigationHistory', JSON.stringify(history));
                }
            }
        });
    </script>

    </html>