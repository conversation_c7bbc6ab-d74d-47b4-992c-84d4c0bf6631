{"author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> <<EMAIL>> (https://github.com/bretcope)", "<PERSON> <<EMAIL>> (https://github.com/rossipedia)", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "last<PERSON>ky", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON>"], "name": "tedious", "description": "A TDS driver, for connecting to MS SQLServer databases.", "keywords": ["sql", "database", "mssql", "sqlserver", "sql-server", "tds", "msnodesql", "azure"], "homepage": "https://github.com/tediousjs/tedious", "bugs": "https://github.com/tediousjs/tedious/issues", "license": "MIT", "version": "18.6.1", "main": "./lib/tedious.js", "types": "./lib/tedious.d.ts", "repository": {"type": "git", "url": "https://github.com/tediousjs/tedious.git"}, "engines": {"node": ">=18"}, "publishConfig": {"tag": "next", "provenance": true}, "dependencies": {"@azure/core-auth": "^1.7.2", "@azure/identity": "^4.2.1", "@azure/keyvault-keys": "^4.4.0", "@js-joda/core": "^5.6.1", "@types/node": ">=18", "bl": "^6.0.11", "iconv-lite": "^0.6.3", "js-md4": "^0.3.2", "native-duplexpair": "^1.0.0", "sprintf-js": "^1.1.3"}, "devDependencies": {"@babel/cli": "^7.23.9", "@babel/core": "^7.23.9", "@babel/node": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-typescript": "^7.23.3", "@babel/register": "^7.23.7", "@types/async": "^3.2.24", "@types/bl": "^5.1.0", "@types/chai": "^4.3.12", "@types/depd": "^1.1.36", "@types/lru-cache": "^5.1.1", "@types/mocha": "^10.0.6", "@types/sprintf-js": "^1.1.4", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "async": "^3.2.5", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.4.1", "codecov": "^3.8.3", "eslint": "^8.57.0", "mitm": "^1.7.2", "mocha": "^10.3.0", "nyc": "^15.1.0", "rimraf": "^5.0.5", "semantic-release": "^19.0.3", "sinon": "^15.2.0", "typedoc": "^0.26.5", "typescript": "^5.5.4"}, "scripts": {"docs": "typedoc", "lint": "eslint src test --ext .js,.ts && tsc", "test": "mocha --forbid-only test/unit test/unit/token test/unit/tracking-buffer", "test-integration": "mocha --forbid-only test/integration/", "test-all": "mocha --forbid-only test/unit/ test/unit/token/ test/unit/tracking-buffer test/integration/", "build:types": "tsc --project tsconfig.build-types.json", "build": "rimraf lib && babel src --out-dir lib --extensions .js,.ts && npm run build:types", "prepublish": "npm run build", "semantic-release": "semantic-release"}, "babel": {"sourceMaps": "both", "ignore": ["./src/**/*.d.ts"], "presets": [["@babel/preset-env", {"targets": {"node": 18}}], ["@babel/preset-typescript", {"allowDeclareFields": true}]], "plugins": [["@babel/transform-typescript", {"allowDeclareFields": true}]]}, "mocha": {"require": "test/setup.js", "timeout": 5000, "extension": ["js", "ts"]}, "nyc": {"sourceMap": false, "instrument": false, "extension": [".ts"]}}