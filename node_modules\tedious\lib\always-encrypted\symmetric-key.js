"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.SymmetricKey = void 0;
// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.
// Copyright (c) 2019 Microsoft Corporation

class SymmetricKey {
  constructor(rootKey) {
    if (!rootKey) {
      throw new Error('Column encryption key cannot be null.');
    } else if (0 === rootKey.length) {
      throw new Error('Empty column encryption key specified.');
    }
    this.rootKey = rootKey;
  }
  zeroOutKey() {
    this.rootKey = Buffer.alloc(this.rootKey.length);
  }
}
exports.SymmetricKey = SymmetricKey;
var _default = exports.default = SymmetricKey;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJTeW1tZXRyaWNLZXkiLCJjb25zdHJ1Y3RvciIsInJvb3RLZXkiLCJFcnJvciIsImxlbmd0aCIsInplcm9PdXRLZXkiLCJCdWZmZXIiLCJhbGxvYyIsImV4cG9ydHMiLCJfZGVmYXVsdCIsImRlZmF1bHQiXSwic291cmNlcyI6WyIuLi8uLi9zcmMvYWx3YXlzLWVuY3J5cHRlZC9zeW1tZXRyaWMta2V5LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgY29kZSBpcyBiYXNlZCBvbiB0aGUgYG1zc3FsLWpkYmNgIGxpYnJhcnkgcHVibGlzaGVkIHVuZGVyIHRoZSBjb25kaXRpb25zIG9mIE1JVCBsaWNlbnNlLlxuLy8gQ29weXJpZ2h0IChjKSAyMDE5IE1pY3Jvc29mdCBDb3Jwb3JhdGlvblxuXG5leHBvcnQgY2xhc3MgU3ltbWV0cmljS2V5IHtcbiAgZGVjbGFyZSByb290S2V5OiBCdWZmZXI7XG5cbiAgY29uc3RydWN0b3Iocm9vdEtleTogQnVmZmVyKSB7XG4gICAgaWYgKCFyb290S2V5KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NvbHVtbiBlbmNyeXB0aW9uIGtleSBjYW5ub3QgYmUgbnVsbC4nKTtcbiAgICB9IGVsc2UgaWYgKDAgPT09IHJvb3RLZXkubGVuZ3RoKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0VtcHR5IGNvbHVtbiBlbmNyeXB0aW9uIGtleSBzcGVjaWZpZWQuJyk7XG4gICAgfVxuICAgIHRoaXMucm9vdEtleSA9IHJvb3RLZXk7XG4gIH1cblxuICB6ZXJvT3V0S2V5KCkge1xuICAgIHRoaXMucm9vdEtleSA9IEJ1ZmZlci5hbGxvYyh0aGlzLnJvb3RLZXkubGVuZ3RoKTtcbiAgfVxufVxuZXhwb3J0IGRlZmF1bHQgU3ltbWV0cmljS2V5O1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBOztBQUVPLE1BQU1BLFlBQVksQ0FBQztFQUd4QkMsV0FBV0EsQ0FBQ0MsT0FBZSxFQUFFO0lBQzNCLElBQUksQ0FBQ0EsT0FBTyxFQUFFO01BQ1osTUFBTSxJQUFJQyxLQUFLLENBQUMsdUNBQXVDLENBQUM7SUFDMUQsQ0FBQyxNQUFNLElBQUksQ0FBQyxLQUFLRCxPQUFPLENBQUNFLE1BQU0sRUFBRTtNQUMvQixNQUFNLElBQUlELEtBQUssQ0FBQyx3Q0FBd0MsQ0FBQztJQUMzRDtJQUNBLElBQUksQ0FBQ0QsT0FBTyxHQUFHQSxPQUFPO0VBQ3hCO0VBRUFHLFVBQVVBLENBQUEsRUFBRztJQUNYLElBQUksQ0FBQ0gsT0FBTyxHQUFHSSxNQUFNLENBQUNDLEtBQUssQ0FBQyxJQUFJLENBQUNMLE9BQU8sQ0FBQ0UsTUFBTSxDQUFDO0VBQ2xEO0FBQ0Y7QUFBQ0ksT0FBQSxDQUFBUixZQUFBLEdBQUFBLFlBQUE7QUFBQSxJQUFBUyxRQUFBLEdBQUFELE9BQUEsQ0FBQUUsT0FBQSxHQUNjVixZQUFZIn0=