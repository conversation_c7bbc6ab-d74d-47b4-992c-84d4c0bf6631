# Sistema SSE Independiente para mod_logistica.php

## Descripción

Se ha creado un sistema SSE (Server-Sent Events) completamente independiente para `mod_logistica.php` que no afecta ni modifica el sistema SSE existente (`sse_logistica.php`) utilizado por `Tecnico_Home_LOGIS_TEST_V2.php`.

## Archivos Creados/Modificados

### 1. **sse_mod_logistica.php** (NUEVO)
- Archivo SSE independiente dedicado exclusivamente a `mod_logistica.php`
- Maneja las 4 tablas del módulo de logística
- Incluye soporte para `id_movimiento = 9` (rechazos) en la tabla de recepción
- Genera HTML específico para la estructura de `mod_logistica.php`

### 2. **js/mod_logistica/sse-handler.js** (MODIFICADO)
- Actualizado para conectarse a `sse_mod_logistica.php` en lugar de `sse_logistica.php`
- Agregada función `updateTablaFaltante()` para manejar la tabla faltante
- Mejorado el manejo de `target_tbody` para mayor precisión
- Agregados event listeners para botones específicos de cada tabla

### 3. **test_sse_mod_logistica.php** (NUEVO)
- Archivo de prueba para verificar el funcionamiento del nuevo SSE
- Incluye las 4 tablas con sus respectivos IDs
- Log en tiempo real de mensajes SSE
- Controles para iniciar/detener/limpiar

## Mapeo de Tablas

| Tabla en mod_logistica.php | ID del tbody | Tabla SSE | Descripción |
|----------------------------|--------------|-----------|-------------|
| Directa | `directaTableBody` | `TablaDirecta` | Materiales directos |
| Faltante | `faltanteTableBody` | `TablaFaltante` | Materiales faltantes |
| Recepción | `recepcionTableBody` | `tablaAsignacion` | Recepción de materiales |
| Reversa | `reversaTableBody` | `poolBodegaReversa` | Materiales reversa |

## Características Principales

### 1. **Independencia Total**
- No modifica `sse_logistica.php` existente
- No afecta el funcionamiento de `Tecnico_Home_LOGIS_TEST_V2.php`
- Sistema completamente separado y modular

### 2. **Soporte Completo para 4 Tablas**
- **Directa**: Materiales asignados directamente al técnico
- **Faltante**: Materiales marcados como faltantes (id_movimiento = 20)
- **Recepción**: Materiales pendientes de recepción (incluye rechazos id_movimiento = 9)
- **Reversa**: Materiales en proceso de reversa

### 3. **Manejo de Rechazos**
- **PROBLEMA RESUELTO**: Los rechazos con `id_movimiento = 9` ahora aparecen correctamente en la tabla de recepción
- El SSE original excluía estos movimientos, el nuevo SSE los incluye específicamente

### 4. **Estructura de Mensajes SSE**
```json
{
    "type": "update",
    "table": "TablaDirecta",
    "target_tbody": "directaTableBody",
    "serial": "ABC123",
    "id_movimiento": 1,
    "semantica": "Disponible",
    "html": "<tr>...</tr>"
}
```

## Consultas SQL por Tabla

### Directa
```sql
SELECT A.*, tp_logis.Semantica 
FROM tb_logis_movimientos A 
LEFT JOIN tp_logis_movimientos tp_logis ON tp_logis.id = A.id_movimiento 
WHERE A.id > $lastId
AND id_movimiento NOT IN (2, 9, 10, 11, 18, 23) -- Exclude rejections
AND (
    (id_movimiento = 15 AND id_tecnico_origen = $userId) OR
    (id_movimiento IN (4, 19) AND ticket = $userId) OR
    (id_movimiento NOT IN (0, 15, 21, 3) AND id_tecnico_destino = $userId) OR
    (id_movimiento = 3 AND id_tecnico_origen = $userId) OR
    (id_movimiento IN (12, 24) AND ticket = $userId)
)
```

### Faltante
```sql
SELECT A.*, tp_logis.Semantica 
FROM tb_logis_movimientos A 
LEFT JOIN tp_logis_movimientos tp_logis ON tp_logis.id = A.id_movimiento 
WHERE A.id > $lastId
AND id_movimiento IN (20) -- Faltante movements
AND (
    id_tecnico_destino = $userId OR 
    id_tecnico_origen = $userId OR
    ticket = $userId
)
```

### Recepción (INCLUYE RECHAZOS)
```sql
SELECT A.*, tp_logis.Semantica 
FROM tb_logis_movimientos A 
LEFT JOIN tp_logis_movimientos tp_logis ON tp_logis.id = A.id_movimiento 
WHERE A.id > $lastId
AND (
    (id_movimiento IN (0, 15, 21) AND id_tecnico_destino = $userId) OR
    (id_movimiento = 9 AND id_tecnico_destino = $userId) OR -- Include rejections
    (id_movimiento = 2 AND id_tecnico_origen = $userId) OR
    (id_movimiento = 24 AND ticket = $userId)
)
```

### Reversa
```sql
SELECT A.*, tp_logis.Semantica 
FROM tb_logis_movimientos A 
LEFT JOIN tp_logis_movimientos tp_logis ON tp_logis.id = A.id_movimiento 
WHERE A.id > $lastId
AND (
    (id_movimiento IN (6, 7, 11, 23) AND (id_tecnico_destino = $userId OR id_tecnico_origen = $userId)) OR
    (id_movimiento IN (22, 12, 11) AND ticket = $userId)
)
```

## Botones y Acciones por Tabla

### Directa
- **Historial**: Ver historial de movimientos
- **Declarar**: Declarar instalación
- **Transferir**: Transferir a otro técnico

### Faltante
- **Historial**: Ver historial de movimientos
- **Justificar**: Justificar faltante

### Recepción
- **Historial**: Ver historial de movimientos
- **Aceptar**: Aceptar recepción
- **Rechazar**: Rechazar material

### Reversa
- **Historial**: Ver historial de movimientos
- **Declarar Entrega**: Declarar entrega de reversa
- **A Supervisor**: Transferir a supervisor

## Instalación y Configuración

### 1. Verificar Archivos
- ✅ `sse_mod_logistica.php` - Nuevo archivo SSE
- ✅ `js/mod_logistica/sse-handler.js` - Modificado para usar nuevo SSE
- ✅ `test_sse_mod_logistica.php` - Archivo de prueba

### 2. Probar Funcionamiento
1. Abrir `test_sse_mod_logistica.php` en el navegador
2. Verificar que la conexión SSE se establece correctamente
3. Observar los mensajes en el log
4. Verificar que las tablas se actualizan en tiempo real

### 3. Verificar en mod_logistica.php
1. Abrir `mod_logistica.php`
2. Verificar que el SSE se conecta automáticamente
3. Probar que los rechazos (id_movimiento = 9) aparecen en la tabla de recepción
4. Verificar que todas las 4 tablas reciben actualizaciones en tiempo real

## Logging y Debugging

### Logs del Servidor
- Los logs se escriben en el error log del servidor con prefijo `SSE_MOD_LOGISTICA:`
- Incluyen timestamp, mensaje y datos relevantes

### Logs del Cliente
- Mensajes de consola con emojis para fácil identificación
- Estados de conexión SSE claramente marcados
- Errores y warnings destacados

## Diferencias con el SSE Original

| Aspecto | SSE Original | SSE Nuevo |
|---------|-------------|-----------|
| Archivo | `sse_logistica.php` | `sse_mod_logistica.php` |
| Tablas | 3 tablas | 4 tablas |
| Rechazos | Excluye id_movimiento = 9 | Incluye id_movimiento = 9 |
| Target | `Tecnico_Home_LOGIS_TEST_V2.php` | `mod_logistica.php` |
| HTML | Estructura antigua | Estructura nueva con clases CSS |
| Botones | Onclick inline | Event listeners modernos |

## Mantenimiento

### Agregar Nueva Tabla
1. Agregar consulta SQL en `sse_mod_logistica.php`
2. Crear función `generateNuevaTablaRow()` 
3. Agregar función `updateNuevaTabla()` en `sse-handler.js`
4. Actualizar `processLogisticaSSEMessage()` para manejar la nueva tabla

### Modificar Consultas
- Las consultas SQL están claramente separadas por tabla
- Cada consulta tiene comentarios explicativos
- Fácil modificación sin afectar otras tablas

## Estado del Proyecto

✅ **COMPLETADO**:
- Sistema SSE independiente creado
- 4 tablas funcionando
- Rechazos incluidos en tabla de recepción
- Archivo de prueba funcional
- Documentación completa

🔄 **PENDIENTE DE PRUEBA**:
- Verificar funcionamiento en producción
- Confirmar que no hay conflictos con SSE original
- Validar rendimiento con múltiples usuarios
