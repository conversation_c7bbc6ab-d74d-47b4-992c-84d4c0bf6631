<?php
/**
 * GET_LOGIS_DIRECTA.php - Gestión de logística directa
 * 
 * Este script maneja diversas operaciones de logística:
 * - Aceptaciones, rechazos y confirmaciones
 * - Transferencias entre técnicos
 * - Justificaciones con adjuntos
 * - Gestión de devoluciones (reversas)
 * 
 * Optimizado para evitar fugas de conexión y mejorar la seguridad
 */

// Incluir archivo de conexión
require_once("con_db.php"); 

// Configuración para manejo de archivos
ini_set('max_execution_time', 120);
ini_set('memory_limit', '256M');

// Definir constantes para tipos de movimiento
define('MOV_ACEPTA', 1);
define('MOV_RECHAZA', 2);
define('MOV_TRANSFIERE', 3);
define('MOV_JUSTIFICA', 4);
define('MOV_INSTALA', 5);
define('MOV_REVERSA_ENTREGADA', 6);
define('MOV_TRANSFIERE_REV', 7);
define('MOV_RECHAZA_BODEGA', 9);
define('MOV_RECHAZA_SUPER', 10);
define('MOV_BODEGA_RECHAZA_REV', 11);
define('MOV_ACEPTA_REVERSA', 12);
define('MOV_VTR', 13);
define('MOV_TRANSFIERE_TECNICO', 15);
define('MOV_VTR_OK', 16);
define('MOV_RECHAZA_BODEGA_JUSTIFI', 18);
define('MOV_GESTION_SUPER', 19);
define('MOV_ACEPTA_JUSTIFICA', 20);
define('MOV_JUSTIFICA_REVERSA', 22);
define('MOV_RECHAZA_SUPER_REVERSA', 23);
define('MOV_CONFIRMA', 24);
define('MOV_CONFIRMA_DEVUELTO', 25);

// Configuración para manejo de archivos
define('UPLOAD_DIR_JUSTIFICACION', 'RespaldoJustificacionEquipos/');
define('UPLOAD_DIR_INVENTARIO', 'FotosInventario/');
define('UPLOAD_DIR_INSTALACION', 'Logistica/InstalacionTecnico/');
define('UPLOAD_DIR_REVERSA', 'Logistica/Reversa_tecnico/');
define('UPLOAD_DIR_DECLARACION', 'Logistica/ReversaEntrega/');

// Verificar si se ha enviado el formulario
if (!isset($_POST['accion'])) {
    echo "No se ha especificado una acción.";
    exit;
}

// Sanitizar entradas básicas
$accion = filter_input(INPUT_POST, 'accion', FILTER_SANITIZE_STRING);
$serie = filter_input(INPUT_POST, 'Serial', FILTER_SANITIZE_STRING);
$serie_corregida = filter_input(INPUT_POST, 'serie_corregida', FILTER_SANITIZE_STRING);
$id_tecnico_origen = filter_input(INPUT_POST, 'id_tecnico_origen', FILTER_VALIDATE_INT);
$tecnico_destino = filter_input(INPUT_POST, 'id_tecnico_destino', FILTER_VALIDATE_INT);
$ticket = filter_input(INPUT_POST, 'ticket', FILTER_SANITIZE_STRING);
$motivo = filter_input(INPUT_POST, 'motivo', FILTER_SANITIZE_STRING);
$observacion = filter_input(INPUT_POST, 'observacion', FILTER_SANITIZE_STRING);
$observacion2 = filter_input(INPUT_POST, 'observacion2', FILTER_SANITIZE_STRING);
$observacionSuperDirecta = filter_input(INPUT_POST, 'observacionSuperDirecta', FILTER_SANITIZE_STRING);
$ObservaReversa = filter_input(INPUT_POST, 'ObservaReversa', FILTER_SANITIZE_STRING);

// Si observacion2 está presente, usarla como observación principal
if (!empty($observacion2)) {
    $observacion = $observacion2;
} elseif (!empty($observacionSuperDirecta)) {
    $observacion = $observacionSuperDirecta;
}

// Más variables específicas para ciertos flujos
$rut_instalado = filter_input(INPUT_POST, 'rut_instalado', FILTER_SANITIZE_STRING);
$OT_instala = filter_input(INPUT_POST, 'OT_instala', FILTER_SANITIZE_STRING);
$rut = filter_input(INPUT_POST, 'rut', FILTER_SANITIZE_STRING);
$orden = filter_input(INPUT_POST, 'orden', FILTER_SANITIZE_STRING);
$request_id = filter_input(INPUT_POST, 'request_id', FILTER_SANITIZE_STRING); // ID único para prevenir duplicados

// Inicializar variables
$id_movimiento = 0;
$archivo_adj = '';
$sql_params = [];
$last_id = 0;

// Función para manejar subida de archivos de forma segura
function procesar_archivo($archivo_input, $directorio_destino, $id_prefijo = '') {
    if (!isset($_FILES[$archivo_input]) || $_FILES[$archivo_input]['error'] != UPLOAD_ERR_OK) {
        return '';
    }
    
    $archivo = $_FILES[$archivo_input];
    
    // Validar tipo de archivo (opcional: implementar según necesidades)
    $tipos_permitidos = ['image/jpeg', 'image/png', 'application/pdf'];
    // if (!in_array($archivo['type'], $tipos_permitidos)) {
    //     return '';
    // }
    
    // Validar tamaño (máximo 10MB)
    if ($archivo['size'] > 10 * 1024 * 1024) {
        return '';
    }
    
    // Crear directorio si no existe
    if (!file_exists($directorio_destino)) {
        mkdir($directorio_destino, 0777, true);
    }
    
    // Crear nombre seguro para el archivo
    $extension = pathinfo($archivo['name'], PATHINFO_EXTENSION);
    $nombre_archivo = $id_prefijo . '_' . uniqid() . '.' . $extension;
    $ruta_destino = $directorio_destino . $nombre_archivo;
    
    // Mover el archivo
    if (move_uploaded_file($archivo['tmp_name'], $ruta_destino)) {
        return $ruta_destino;
    }
    
    return '';
}

// PROCESAMIENTO PRINCIPAL
try {
    // Iniciar transacción
    mysqli_begin_transaction($conex);
    
    // Determinar el tipo de movimiento y preparar datos SQL
    switch ($accion) {
        case "ACEPTA":
            $id_movimiento = MOV_ACEPTA;
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => $id_tecnico_origen,
                'observacion' => '',
                'id_movimiento' => $id_movimiento,
                'motivo' => '',
                'ticket' => $ticket
            ];
            break;
            
        case "RECHAZA":
            $id_movimiento = MOV_RECHAZA;
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => $tecnico_destino,
                'observacion' => '',
                'id_movimiento' => $id_movimiento,
                'motivo' => $motivo,
                'ticket' => $id_tecnico_origen,
                'flag_bodega_final' => 0
            ];
            break;
            
        case "RECHAZA_SUPER":
            $id_movimiento = MOV_RECHAZA_SUPER;
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => $tecnico_destino,
                'observacion' => $observacion,
                'id_movimiento' => $id_movimiento,
                'motivo' => $motivo,
                'ticket' => $ticket
            ];
            break;
            
        case "CONFIRMA":
            $id_movimiento = MOV_CONFIRMA;
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => $tecnico_destino,
                'observacion' => $observacion,
                'id_movimiento' => $id_movimiento,
                'motivo' => $motivo,
                'ticket' => $ticket
            ];
            break;
            
        case "rechaza_bodega_justifi":
            $id_movimiento = MOV_RECHAZA_BODEGA_JUSTIFI;
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => $tecnico_destino,
                'observacion' => $observacion,
                'id_movimiento' => $id_movimiento,
                'motivo' => $motivo,
                'ticket' => $ticket,
                'flag_bodega_final' => 0
            ];
            break;
            
        case "CONFIRMA_DEVUELTO":
            $id_movimiento = MOV_CONFIRMA_DEVUELTO;
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => $id_tecnico_origen,
                'observacion' => $observacion,
                'id_movimiento' => $id_movimiento,
                'motivo' => $motivo,
                'ticket' => $ticket
            ];
            break;
            
        case "gestion_super":
            $id_movimiento = MOV_GESTION_SUPER;
            $archivo_adj = procesar_archivo('fileJustificaDirecta', UPLOAD_DIR_JUSTIFICACION, 'super_');
            
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => $tecnico_destino,
                'observacion' => $observacion,
                'id_movimiento' => $id_movimiento,
                'motivo' => $motivo,
                'ticket' => $ticket,
                'archivo_adj' => $archivo_adj,
                'flag_bodega_final' => 0
            ];
            break;
            
        case "ACEPTA JUSTIFICA":
            $id_movimiento = MOV_ACEPTA_JUSTIFICA;
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => 164, // Bodega
                'observacion' => '',
                'id_movimiento' => $id_movimiento,
                'motivo' => '',
                'ticket' => $ticket
            ];
            break;
            
        case "transfiere":
            if ($motivo == "Por transferencia a otro tecnico") {
                $id_movimiento = MOV_TRANSFIERE_TECNICO;
                $sql_params = [
                    'fecha_hora' => 'NOW()',
                    'serie' => $serie,
                    'id_tecnico_origen' => $id_tecnico_origen,
                    'id_tecnico_destino' => $tecnico_destino,
                    'observacion' => '',
                    'id_movimiento' => $id_movimiento,
                    'motivo' => $motivo,
                    'ticket' => 'Si',
                    'flag_bodega_final' => 0
                ];
            } else {
                $id_movimiento = MOV_TRANSFIERE;
                $archivo_adj = procesar_archivo('fileInventario', UPLOAD_DIR_INVENTARIO);
                
                $sql_params = [
                    'fecha_hora' => 'NOW()',
                    'serie' => $serie,
                    'id_tecnico_origen' => $id_tecnico_origen,
                    'id_tecnico_destino' => $tecnico_destino,
                    'observacion' => '',
                    'id_movimiento' => $id_movimiento,
                    'motivo' => $motivo,
                    'ticket' => $id_tecnico_origen,
                    'archivo_adj' => $archivo_adj
                ];
            }
            break;
            
        case "rechaza_bodega":
            $id_movimiento = MOV_RECHAZA_BODEGA;
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => $tecnico_destino,
                'observacion' => $observacion,
                'id_movimiento' => $id_movimiento,
                'motivo' => $motivo,
                'ticket' => $ticket,
                'flag_bodega_final' => 0
            ];
            break;
            
        case "justifica":
            $id_movimiento = MOV_JUSTIFICA;
            $archivo_adj = procesar_archivo('fileJustificaDirecta', UPLOAD_DIR_JUSTIFICACION);
            
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => 164, // Bodega
                'observacion' => $observacion,
                'id_movimiento' => $id_movimiento,
                'motivo' => $motivo,
                'ticket' => $ticket,
                'archivo_adj' => $archivo_adj
            ];
            break;
            
        case "justifica_reversa":
            $id_movimiento = MOV_JUSTIFICA_REVERSA;
            $archivo_adj = procesar_archivo('fileJustificaDirecta', UPLOAD_DIR_JUSTIFICACION);
            
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => 164, // Bodega
                'observacion' => $observacion,
                'id_movimiento' => $id_movimiento,
                'motivo' => $motivo,
                'ticket' => $ticket,
                'archivo_adj' => $archivo_adj
            ];
            break;
            
        case "instala":
            $id_movimiento = MOV_INSTALA;
            $archivo_adj = procesar_archivo('fileInsta', UPLOAD_DIR_INSTALACION);
            
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $tecnico_destino,
                'id_tecnico_destino' => $id_tecnico_origen,
                'observacion' => '',
                'id_movimiento' => $id_movimiento,
                'motivo' => $motivo,
                'ticket' => $ticket,
                'archivo_adj' => $archivo_adj
            ];
            break;
            
        case "transfiereRev":
            $id_movimiento = MOV_TRANSFIERE_REV;
            $archivo_adj = procesar_archivo('userfile', UPLOAD_DIR_REVERSA);
            
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => $tecnico_destino,
                'observacion' => $ObservaReversa,
                'id_movimiento' => $id_movimiento,
                'motivo' => $motivo,
                'ticket' => $ticket,
                'archivo_adj' => $archivo_adj
            ];
            break;
            
        case "BODEGA RECHAZA REVERSA":
            $id_movimiento = MOV_BODEGA_RECHAZA_REV;
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => $ticket,
                'observacion' => $observacion,
                'id_movimiento' => $id_movimiento,
                'motivo' => $motivo,
                'ticket' => $ticket
            ];
            break;
            
        case "rechaza_supervisor_reversa":
            $id_movimiento = MOV_RECHAZA_SUPER_REVERSA;
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => $ticket,
                'observacion' => $observacion,
                'id_movimiento' => $id_movimiento,
                'motivo' => $motivo,
                'ticket' => $ticket
            ];
            break;
            
        case "ACEPTA REVERSA":
            $id_movimiento = MOV_ACEPTA_REVERSA;
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => 164, // Bodega
                'id_tecnico_destino' => 164, // Bodega
                'observacion' => $motivo,
                'id_movimiento' => $id_movimiento,
                'motivo' => '',
                'ticket' => $ticket
            ];
            break;
            
        case "ReversaEntregada":
        case "ENTREGA_REV": // Manejar declaración de entrega reversa - Debe eliminar de la tabla
            $id_movimiento = MOV_REVERSA_ENTREGADA;
            $archivo_adj = procesar_archivo('fileReversaDecla', UPLOAD_DIR_DECLARACION);
            
            // Registro detallado para debugging
            error_log("[REVERSA_LOG] Procesando entrega reversa - ELIMINAR de tabla. Serie: $serie, ID Técnico: $id_tecnico_origen, Archivo: " . ($archivo_adj ? "OK" : "FALTA"));
            
            // Verificar si el archivo se subió correctamente
            if (empty($archivo_adj)) {
                error_log("[REVERSA_LOG] ERROR: Archivo adjunto faltante para entrega reversa. Serie: $serie");
                throw new Exception("Falta archivo adjunto para entrega reversa");
            }
            
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => 164, // Bodega
                'observacion' => 'Entrega a bodega',
                'id_movimiento' => $id_movimiento,
                'motivo' => '',
                'ticket' => $ticket,
                'archivo_adj' => $archivo_adj
            ];
            break;
            
        case "VTR":
            $id_movimiento = MOV_VTR;
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => $id_tecnico_origen,
                'id_tecnico_destino' => 164, // Bodega
                'observacion' => 'Entrega a bodega',
                'id_movimiento' => $id_movimiento,
                'motivo' => '',
                'ticket' => $ticket
            ];
            break;
            
        case "vtrOK":
            $id_movimiento = MOV_VTR_OK;
            $sql_params = [
                'fecha_hora' => 'NOW()',
                'serie' => $serie,
                'id_tecnico_origen' => 164, // Bodega
                'id_tecnico_destino' => 164, // Bodega
                'observacion' => $motivo,
                'id_movimiento' => $id_movimiento,
                'motivo' => '',
                'ticket' => $ticket
            ];
            break;
            
        default:
            throw new Exception("Acción no reconocida: " . $accion);
    }
    
    // PASO 1: Insertar en TB_LOGIS_MOVIMIENTOS
    $campos = implode(", ", array_keys($sql_params));
    $placeholders = array();
    $valores = array();
    $tipos = "";
    
    foreach ($sql_params as $key => $value) {
        if ($key == 'fecha_hora' && $value == 'NOW()') {
            $placeholders[] = 'NOW()';
        } else {
            $placeholders[] = '?';
            $valores[] = $value;
            $tipos .= 's'; // Asumimos string para simplificar, idealmente verificar tipo
        }
    }
    
    $placeholders_str = implode(", ", $placeholders);
    
    $sql = "INSERT INTO TB_LOGIS_MOVIMIENTOS ($campos) VALUES ($placeholders_str)";

    // Verificar duplicados para acciones críticas
    if (!empty($request_id) && in_array($accion, ['RECHAZA', 'ACEPTA', 'CONFIRMA'])) {
        $check_duplicate_sql = "SELECT COUNT(*) as count FROM TB_LOGIS_MOVIMIENTOS
                               WHERE serie = ? AND id_movimiento = ? AND fecha_hora > DATE_SUB(NOW(), INTERVAL 5 MINUTE)";

        $stmt_check = $conex->prepare($check_duplicate_sql);
        if ($stmt_check) {
            $stmt_check->bind_param("si", $serie, $id_movimiento);
            $stmt_check->execute();
            $result = $stmt_check->get_result();
            $row = $result->fetch_assoc();

            if ($row['count'] > 0) {
                $stmt_check->close();
                echo json_encode([
                    'success' => false,
                    'message' => "Operación duplicada detectada. La serie '$serie' ya fue procesada recientemente.",
                    'duplicate' => true
                ]);
                exit;
            }
            $stmt_check->close();
        }
    }

    // Reemplazar NOW() en la consulta para que funcione con prepared statements
    $sql = str_replace('?, NOW()', 'NOW(), ?', $sql);
    $sql = str_replace('NOW(), NOW()', 'NOW(), NOW()', $sql);

    // Preparar y ejecutar la consulta
    $stmt = $conex->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Error al preparar la consulta: " . $conex->error);
    }
    
    // Si hay parámetros para bind
    if (!empty($valores) && !empty($tipos)) {
        // Eliminar los valores que corresponden a NOW()
        $valores_limpios = array();
        foreach ($valores as $v) {
            if ($v !== 'NOW()') {
                $valores_limpios[] = $v;
            }
        }
        
        // Crear array de referencias para bind_param
        $params = array();
        $params[] = $tipos;
        
        for ($i = 0; $i < count($valores_limpios); $i++) {
            $params[] = &$valores_limpios[$i];
        }
        
        // Llamar a bind_param con parámetros dinámicos
        if (!empty($params)) {
            call_user_func_array(array($stmt, 'bind_param'), $params);
        }
    }
    
    // Ejecutar la consulta
    if (!$stmt->execute()) {
        throw new Exception("Error al ejecutar la consulta: " . $stmt->error);
    }
    
    // Obtener el ID insertado
    $last_id = $stmt->insert_id;
    $stmt->close();
    
    // PASO 2: Actualizar tablas de inventario
    
    // Actualizar tb_ferret_directa1
    $sql_update = "UPDATE tb_ferret_directa1 
                   SET Attributes = ?, 
                       `Unit Number` = ? 
                   WHERE serial = ?";
    
    $stmt_update = $conex->prepare($sql_update);
    if (!$stmt_update) {
        throw new Exception("Error al preparar actualización: " . $conex->error);
    }
    
    $stmt_update->bind_param("iis", $id_movimiento, $last_id, $serie);
    
    if (!$stmt_update->execute()) {
        throw new Exception("Error al actualizar tb_ferret_directa1: " . $stmt_update->error);
    }
    
    $stmt_update->close();
    
    // Actualizar tb_logist_bdreversa
    $sql_update2 = "UPDATE tb_logist_bdreversa 
                    SET Attributes = ?, 
                        `Unit Number` = ? 
                    WHERE serial = ?";
    
    $stmt_update2 = $conex->prepare($sql_update2);
    if (!$stmt_update2) {
        throw new Exception("Error al preparar actualización: " . $conex->error);
    }
    
    $stmt_update2->bind_param("iis", $id_movimiento, $last_id, $serie);
    
    if (!$stmt_update2->execute()) {
        throw new Exception("Error al actualizar tb_logist_bdreversa: " . $stmt_update2->error);
    }
    
    $stmt_update2->close();
    
    // PASO 3: Procesos específicos según la acción


    if ($accion == "transfiere" && $motivo == "Por transferencia a otro tecnico" ) 
    {
        $last_id = $conex->insert_id; // Obtiene el último ID insertado después de la ejecución
        $rut_instalado = isset($_POST['rut_instalado']) ? $_POST['rut_instalado'] : '';
        $OT_instala = isset($_POST['OT_instala']) ? $_POST['OT_instala'] : '';
        
        // Segunda consulta SQL
        $sql2 = "INSERT INTO tb_logis_tecnico_serie_transfiere 
        (fecha, id_destino, id_origen, id_movimiento, obs, serie )
                 VALUES (  NOW(), '$tecnico_destino', '$id_tecnico_origen','$last_id','','$serie')"; // Use $last_id aquí

        $stmt2 = $conex->prepare($sql2);
        if ($stmt2) {
            // Ejecutar la segunda consulta
             

            if($stmt2->execute()) {

                sleep(1);
                // Refresh the materialized view
                $refreshQuery = "CALL refresh_mv_asignacion_tecnico_serie()";
                $stmt3 = $conex->prepare($refreshQuery);
                $stmt3->execute();
                
                echo "Datos insertados correctamente en la segunda tabla.";
            } else {
                echo "Error al ejecutar la segunda consulta: " . $stmt2->error;
            }
        } else {
            echo "Error en la preparación de la segunda consulta: " . $conex->error;
        }
    }
    
    // Para aceptación de ticket
    if ($ticket == "Si" && $accion == "ACEPTA") {
        $sql_aceptacion = "UPDATE tb_logis_tecnico_serie_transfiere 
                           SET flagacepta = 'Si'
                           WHERE serie = ?";
        
        $stmt_aceptacion = $conex->prepare($sql_aceptacion);
        if (!$stmt_aceptacion) {
            throw new Exception("Error al preparar actualización de aceptación: " . $conex->error);
        }
        
        $stmt_aceptacion->bind_param("s", $serie);
        
        if (!$stmt_aceptacion->execute()) {
            throw new Exception("Error al actualizar aceptación: " . $stmt_aceptacion->error);
        }
        
        $stmt_aceptacion->close();
        
        // Refrescar vista materializada - EVITAR SLEEP y usar enfoque asincrónico
        $refreshQuery = "CALL refresh_mv_asignacion_tecnico_serie()";
        $stmt_refresh = $conex->prepare($refreshQuery);
        
        if (!$stmt_refresh) {
            throw new Exception("Error al preparar refresh: " . $conex->error);
        }
        
        if (!$stmt_refresh->execute()) {
            throw new Exception("Error al ejecutar refresh: " . $stmt_refresh->error);
        }
        
        $stmt_refresh->close();
    }
    
    // Para instalación o transferencia reversa
    if ($accion == "transfiereRev" || $accion == "instala") {
        // Insertar información de orden/rut
        $sql_orden = "INSERT INTO TB_LOGIS_RUT_ORDEN_FORM 
                      (id_mov, fecha, rut, orden, archivo, SERIE)
                      VALUES (?, NOW(), ?, ?, ?, ?)";
        
        $stmt_orden = $conex->prepare($sql_orden);
        if (!$stmt_orden) {
            throw new Exception("Error al preparar inserción de orden: " . $conex->error);
        }
        
        $stmt_orden->bind_param("issss", $last_id, $rut_instalado, $OT_instala, $archivo_adj, $serie);
        
        if (!$stmt_orden->execute()) {
            throw new Exception("Error al insertar orden: " . $stmt_orden->error);
        }
        
        $stmt_orden->close();
        
        // Insertar serie correcta si aplica
        $sql_serie = "INSERT INTO TB_LOGIS_SERIE_CORRECTA 
                      (serie, fecha, id_movimiento, serie_correcta)
                      VALUES (?, NOW(), ?, ?)";
        
        $stmt_serie = $conex->prepare($sql_serie);
        if (!$stmt_serie) {
            throw new Exception("Error al preparar inserción de serie: " . $conex->error);
        }
        
        $stmt_serie->bind_param("sis", $serie, $last_id, $ticket);
        
        if (!$stmt_serie->execute()) {
            throw new Exception("Error al insertar serie correcta: " . $stmt_serie->error);
        }
        
        $stmt_serie->close();
    }
    
    // Para justificación
    if ($accion == "justifica") {
        $sql_justificacion = "INSERT INTO TB_LOGIST_FORM_JUSTIF 
                             (serie, rut, numero_orden, tecnico, tipo_justificacion, 
                              motivo, numero_ticket, observacion, Usuario, 
                              FechaRegistro, link_archivo, link, id_mov)
                             VALUES (?, ?, ?, '', 'Justificacion', ?, ?, ?, ?, 
                                     NOW(), ?, '', ?)";
        
        $stmt_justificacion = $conex->prepare($sql_justificacion);
        if (!$stmt_justificacion) {
            throw new Exception("Error al preparar inserción de justificación: " . $conex->error);
        }
        
        $stmt_justificacion->bind_param("sssssssis", 
            $serie, 
            $rut, 
            $orden, 
            $motivo, 
            $ticket, 
            $observacion, 
            $id_tecnico_origen,
            $archivo_adj,
            $last_id
        );
        
        if (!$stmt_justificacion->execute()) {
            throw new Exception("Error al insertar justificación: " . $stmt_justificacion->error);
        }
        
        $stmt_justificacion->close();
    }
    
    // Confirmar cambios
    mysqli_commit($conex);
    
    // Registrar éxito para ENTREGA_REV
    if ($accion == "ENTREGA_REV" || $accion == "ReversaEntregada") {
        error_log("[REVERSA_LOG] ÉXITO procesando entrega reversa. Serie: $serie, ID: $last_id, ID Movimiento: $id_movimiento");
    }
    
    echo "Operación completada con éxito. ID: " . $last_id;
    
} catch (Exception $e) {
    // Revertir cambios en caso de error
    mysqli_rollback($conex);
    
    // Registrar el error para debugging
    error_log("Error en GET_LOGIS_DIRECTA.php: " . $e->getMessage());
    
    // Registrar detalles adicionales para ENTREGA_REV
    if ($accion == "ENTREGA_REV") {
        error_log("[REVERSA_LOG] ERROR procesando ENTREGA_REV: " . $e->getMessage());
        error_log("[REVERSA_LOG] Detalles: Serie=$serie, ID Técnico=$id_tecnico_origen, Archivo adjunto=" . (isset($_FILES['fileReversaDecla']) ? 'SI' : 'NO'));
        if (isset($_FILES['fileReversaDecla'])) {
            error_log("[REVERSA_LOG] Archivo: Nombre=" . $_FILES['fileReversaDecla']['name'] . 
                      ", Tipo=" . $_FILES['fileReversaDecla']['type'] . 
                      ", Tamaño=" . $_FILES['fileReversaDecla']['size'] . 
                      ", Error=" . $_FILES['fileReversaDecla']['error']);
        }
    }
    
    // Mostrar mensaje genérico al usuario
    echo "Error al procesar la solicitud: " . $e->getMessage();
    
} finally {
    // No es necesario cerrar la conexión explícitamente aquí 
    // ya que con_db.php incluye register_shutdown_function
    // que se encargará de cerrar la conexión correctamente
}
?>