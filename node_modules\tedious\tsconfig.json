{
  "compilerOptions": {
    "target": "esnext",
    "moduleResolution": "node",
    "allowJs": true,
    "noEmit": true,
    "strict": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "lib": [ "esnext" ],
    "skipLibCheck": true,
    "resolveJsonModule": true,
    "exactOptionalPropertyTypes": true,
    "verbatimModuleSyntax": true
  },

  "include": [
    "types/*.d.ts",
    "src/**/*.ts",
    "test/**/*.ts",
    "test/**/*.js",
  ],

  "typedocOptions": {
    "name": "Tedious",
    "out": "./docs",
    "entryPoints": ["./src/tedious.ts"]
  }
}
