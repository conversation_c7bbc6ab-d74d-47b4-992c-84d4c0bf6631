7<?php
/**
 * Endpoint AJAX para cargar tablas de logística de forma individual
 * Archivo: ajax_load_table.php
 */

// Configuración de headers para AJAX
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Incluir conexión a la base de datos (usar el mismo archivo que mod_logistica.php)
try {
    require_once 'con_db.php';

    if (!isset($conex) || !$conex) {
        throw new Exception("No se pudo establecer la conexión a la base de datos");
    }
} catch (Exception $e) {
    http_response_code(503);
    echo json_encode([
        'success' => false,
        'error' => 'Error de conexión a la base de datos: ' . $e->getMessage()
    ]);
    exit;
}

// Debug: mostrar información del método
$metodo = $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN';

// Verificar que sea una petición POST
if ($metodo !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'error' => 'Método no permitido',
        'metodo_recibido' => $metodo,
        'metodos_disponibles' => ['POST']
    ]);
    exit;
}

// Obtener parámetros
$tabla = $_POST['tabla'] ?? '';
$id_usuario = $_POST['id_usuario'] ?? '';

// Debug: log de parámetros recibidos
error_log("AJAX Load Table - Tabla: $tabla, Usuario: $id_usuario");

// Validar parámetros
if (empty($tabla) || empty($id_usuario)) {
    http_response_code(400);
    echo json_encode([
        'error' => 'Parámetros faltantes',
        'tabla_recibida' => $tabla,
        'usuario_recibido' => $id_usuario,
        'post_data' => $_POST
    ]);
    exit;
}

// Validar que la tabla sea una de las permitidas
$tablas_permitidas = ['directa', 'faltante', 'recepcion', 'reversa'];
if (!in_array($tabla, $tablas_permitidas)) {
    http_response_code(400);
    echo json_encode(['error' => 'Tabla no válida']);
    exit;
}

try {
    $resultado = null;
    $sql = '';
    
    switch ($tabla) {
        case 'directa':
            $sql = "SELECT RZ.Serial
                , RZ.Item
                , Subinventory
                , RZ.`Attributes` as id_movimiento
                , tlm.Semantica as tipo_movimiento
                , tfd.descripcion, tfd.familia
                , Semantica
                , job
                , tpve.monto_valor
                , fecha_carga
                , tp_familia
            FROM
            (
                SELECT Serial, Item, Org, Revision
                , CASE
                    WHEN Y.serie is not null THEN tut2.rut
                    else Subinventory
                END as Subinventory
                , `Locator`
                , Operation
                , CASE
                    WHEN job is null THEN 0
                    WHEN job = '' THEN 0
                    ELSE job
                END as job
                , Step, Lot, State, Status, `Receipt Date`
                , `Ship Date`, `Supplier Name`, `Supplier Lot`
                , `Supplier Serial`, `Unit Number`, `Attributes`, `[  ]`
                , `Unnamed: 20`, fecha_carga
                , tut2.rut
                FROM tb_ferret_directa1 AS DIRECTA
                LEFT JOIN
                (
                    select * from
                    (
                        SELECT *, ROW_NUMBER() OVER (PARTITION BY serie ORDER BY fecha DESC) AS row_num_trans
                        FROM TB_LOGIS_TECNICO_SERIE_TRANSFIERE
                    ) YY
                    WHERE YY.row_num_trans = 1
                ) Y ON DIRECTA.Serial = Y.serie
                LEFT JOIN tb_user_tqw tut2 ON tut2.id = Y.id_destino
            ) RZ
            LEFT JOIN tb_logis_movimientos MOVIMI ON `MOVIMI`.id = RZ.`Unit Number`
            LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm ON MOVIMI.id_movimiento = tlm.id
            LEFT JOIN tb_user_tqw tut ON tut.rut = RZ.Subinventory
            LEFT JOIN tp_ferret_desc tfd ON tfd.ID = RZ.Item
            LEFT JOIN TP_LOGIST_VALORES_EQUIPO tpve ON tpve.descripcion_modelo_equipo = tfd.descripcion
            WHERE `MOVIMI`.id IS NOT NULL
            and CASE
                WHEN MOVIMI.id_movimiento = 2 THEN 'NO'
                WHEN MOVIMI.id_movimiento = 9 THEN 'NO'
                WHEN MOVIMI.id_movimiento = 15 THEN 'NO'
                WHEN MOVIMI.id_movimiento = 0 THEN 'NO'
                WHEN MOVIMI.id_movimiento = 21 THEN 'NO'
                WHEN MOVIMI.id_movimiento = 5 THEN 'NO'
                WHEN MOVIMI.id_movimiento = 12 THEN 'NO'
                ELSE 'SI'
            END = 'SI'
            AND tut.id = '$id_usuario'
            AND RZ.State <> 'Issued out of stores'";
            break;
            
        case 'faltante':
            $sql = "SELECT id_cierre,
                tlci.fecha,
                tlci.Serial,
                id_tecnico,
                supervisor,
                SerieCargada,
                ESTADO_APP,
                CASE
                    WHEN tlm.Semantica IS NULL THEN
                        CASE
                            WHEN flujo = 'Reversa' THEN 'Pendiente por entregar'
                            WHEN flujo = 'Directa' THEN 'Entregado'
                        END
                    WHEN disponi.`Serial` is null and flujo = 'Directa'
                        and A.id_movimiento <> 20
                        THEN 'Fuera Organización'
                    ELSE tlm.Semantica
                END AS Semantica,
                flujo,
                monto_valor id, fecha_hora
                , tlci.`Serial` as serie,
                id_tecnico_origen
                , id_tecnico_destino,
                observacion
                , tlci.id_movimiento as id_movimiento_actual
                , A.motivo, ticket
                , monto_valor
                , t.id as id_valor_tecnico
                , CASE
                    when tlm.Semantica is null THEN 1
                    else descuento
                END AS descuento
                , escaladas.orden, escaladas.rut, escaladas.archivo
            FROM TB_CIERRE_INVENTARIO_FALTANTE tlci
            LEFT JOIN
            (
                SELECT id, fecha_hora, serie,
                id_tecnico_origen, id_tecnico_destino,
                observacion, id_movimiento, motivo, ticket
                FROM (
                    SELECT id, fecha_hora, serie,
                    id_tecnico_origen, id_tecnico_destino, ticket,
                    observacion, id_movimiento, motivo,
                    ROW_NUMBER() OVER (PARTITION BY serie ORDER BY fecha_hora DESC) AS row_num
                    FROM TB_LOGIS_MOVIMIENTOS
                ) AS ranked
                WHERE row_num = 1
            ) A
            ON A.serie = tlci.Serial
            LEFT JOIN tb_user_tqw t
            ON tlci.id_tecnico = t.`Nombre_short`
            LEFT JOIN tp_logis_movimientos tlm
            ON tlm.id = A.id_movimiento
            LEFT JOIN tb_logis_rut_orden_form escaladas
            ON escaladas.id_mov = A.id
            LEFT JOIN (
                select Serial from tb_ferret_directa1
                union all
                select Serial from tb_logist_bdreversa
            ) disponi
            ON disponi.`Serial` = tlci.`Serial`
            WHERE id_tecnico not in ('ANALISIS','DISPONIBLE')
            and t.id = '$id_usuario'
            and CASE
                when tlm.Semantica is null THEN 1
                else descuento
            END = 1";
            break;
            
        case 'recepcion':
            $sql = "SELECT RZ.Serial
            , RZ.Item
            , RZ.Org
            , RZ.Subinventory
            , RZ.`Locator`
            , RZ.State
            , tlm.Semantica as tipo_movimiento
            , RZ.Status
            , RZ.`Receipt Date`
            , MOVIMI.id_tecnico_origen as id_tecnico_origen
            , MOVIMI.id_tecnico_destino as id_tecnico_destino
            , MOVIMI.observacion as observacion
            , `Unit Number`
            , MOVIMI.id_movimiento
            , MOVIMI.motivo as motivo
            , MOVIMI.ticket as ticket
            , tfd.descripcion
            , tfd.familia
            , '' as fechax
            , CASE
                WHEN Job is null THEN 0
                WHEN Job = '' THEN 0
                ELSE Job
            END as Job
            , Semantica
            , Flag_trans
            , tpve.monto_valor
            , CASE
                WHEN MOVIMI.id_movimiento = 15 THEN MOVIMI.id_tecnico_origen
                ELSE 164
            END as id_transfer
            FROM (
                SELECT Serial, Item, Org, Revision
                , CASE
                    WHEN `Locator` = 'ANALISIS' THEN Subinventory
                    WHEN Y.serie is not null THEN tut2.rut
                    else Subinventory
                END as Subinventory
                , `Locator`
                , Operation, Job, Step, Lot, State, Status, `Receipt Date`
                , `Ship Date`, `Supplier Name`, `Supplier Lot`
                , `Supplier Serial`, `Unit Number`, `Attributes`, `[  ]`
                , `Unnamed: 20`, fecha_carga
                , tut2.rut
                , CASE
                    WHEN Y.serie is not null THEN 'Si'
                    else 'No'
                END as Flag_trans
                FROM TB_FERRET_DIRECTA1 AS DIRECTA
                LEFT JOIN (
                    SELECT serie, id_destino, rn
                    FROM (
                        SELECT serie, id_destino, row_number() OVER (PARTITION BY serie ORDER BY fecha DESC) rn
                        FROM TB_LOGIS_TECNICO_SERIE_TRANSFIERE
                    ) x WHERE rn = 1
                ) Y ON DIRECTA.Serial = Y.serie
                LEFT JOIN tb_user_tqw tut2 ON tut2.id = Y.id_destino
            ) RZ
            LEFT JOIN tb_logis_movimientos MOVIMI ON `MOVIMI`.id = RZ.`Unit Number`
            LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm ON MOVIMI.id_movimiento = tlm.id
            LEFT JOIN tb_user_tqw tut ON tut.rut = RZ.Subinventory
            LEFT JOIN tp_ferret_desc tfd ON tfd.ID = RZ.Item
            LEFT JOIN TP_LOGIST_VALORES_EQUIPO tpve ON tpve.descripcion_modelo_equipo = tfd.descripcion
            WHERE (
                MOVIMI.id_movimiento IS NULL OR
                (MOVIMI.id_movimiento IN (0, 9, 15, 21) AND MOVIMI.flag_bodega_final != 1)
            )
            AND CASE
                WHEN RZ.State IS NULL THEN 0
                WHEN RZ.State = 'Issued out of stores' THEN 1
                else 0
            END = 0
            AND tut.id = '$id_usuario'";
            break;
            
        case 'reversa':
            $sql = "SELECT RZ.Serial
            , RZ.Item
            , RZ.Org
            , RZ.Subinventory
            , SUBSTRING(`Locator`, 1, LENGTH(`Locator`) - 2) as `Locator`
            , RZ.State
            , RZ.Status
            , tlm.Semantica as tipo_movimiento
            , RZ.`Receipt Date`
            , fecha_hora, A.serie
            , id_tecnico_origen
            , id_tecnico_destino
            , observacion
            , A.id_movimiento
            , motivo, ticket, tut.Nombre_short
            , tfd.descripcion
            , tfd.familia
            , case
                WHEN A.id_movimiento is null then 'Pendiente por entregar'
                WHEN A.id_movimiento = 0 then 'Pendiente por entregar'
                else Semantica
            end as Semantica
            FROM
            (
                SELECT Serial, Item, Org, Revision, Subinventory, `Locator`, Operation, Job
                , Step, Lot, State, Status, `Receipt Date`, `Ship Date`, `Supplier Name`
                , `Supplier Lot`, `Supplier Serial`, `Unit Number`, `Attributes`, `[  ]`, `Unnamed: 20`
                FROM TB_LOGIST_bdReversa
            ) RZ
            LEFT JOIN
            (
                SELECT id, fecha_hora, serie,
                id_tecnico_origen, id_tecnico_destino,
                observacion, id_movimiento, motivo, ticket
                FROM (
                    SELECT id, fecha_hora, serie,
                    id_tecnico_origen, id_tecnico_destino, ticket,
                    observacion, id_movimiento, motivo,
                    ROW_NUMBER() OVER (PARTITION BY serie ORDER BY fecha_hora DESC) AS row_num
                    FROM TB_LOGIS_MOVIMIENTOS
                ) AS ranked
                WHERE row_num = 1
            ) A
            ON RZ.Serial = A.serie
            LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm ON A.id_movimiento = tlm.id
            LEFT JOIN tb_user_tqw tut ON
                SUBSTRING(tut.rut, 1, LENGTH(tut.rut) - 2) = SUBSTRING(`Locator`, 1, LENGTH(`Locator`) - 2)
            LEFT JOIN tb_user_tqw tut_destino ON tut_destino.id = A.id_tecnico_destino
            LEFT JOIN tp_ferret_desc tfd ON RZ.Item = tfd.ID
            WHERE CASE
                WHEN A.id_movimiento = 12 THEN 'NO'
                ELSE 'SI'
            END = 'SI'
            AND RZ.State <> 'Issued out of stores'
            AND tut.id = '$id_usuario'";
            break;
    }
    
    // Debug: log de la consulta SQL
    error_log("AJAX Load Table - SQL para tabla $tabla: " . substr($sql, 0, 200) . "...");

    // Ejecutar consulta
    $resultado = mysqli_query($conex, $sql);

    if (!$resultado) {
        $error_msg = mysqli_error($conex);
        error_log("AJAX Load Table - Error en consulta: $error_msg");
        throw new Exception('Error en la consulta: ' . $error_msg);
    }

    // Convertir resultado a array
    $datos = [];
    while ($fila = mysqli_fetch_assoc($resultado)) {
        $datos[] = $fila;
    }

    // Debug: log del resultado
    error_log("AJAX Load Table - Tabla $tabla cargada: " . count($datos) . " registros");
    error_log("AJAX Load Table - Datos: " . json_encode($datos));

    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'tabla' => $tabla,
        'datos' => $datos,
        'total' => count($datos)
    ]);
    
    // Cerrar conexión exitosa
    if (isset($conex)) {
        mysqli_close($conex);
    }
    exit; // Salir inmediatamente después de respuesta exitosa

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error del servidor: ' . $e->getMessage(),
        'tabla' => $tabla ?? 'desconocida'
    ]);
    
    // Cerrar conexión en error
    if (isset($conex)) {
        mysqli_close($conex);
    }
    exit; // Salir inmediatamente después de respuesta de error
}

// Este código nunca debería ejecutarse debido a los exit anteriores
// Pero está aquí como medida de seguridad adicional
if (isset($conex)) {
    mysqli_close($conex);
}
exit;
